#!/usr/bin/env python3
"""
NIRA A1 Lesson Creator - <PERSON> Script
Creates 14 new A1 lessons to reach 25 total, with full content and audio
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def check_dependencies():
    """Check if all required packages are installed"""
    required_packages = [
        'google-generativeai',
        'openai', 
        'requests'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nInstall with: pip install " + " ".join(missing_packages))
        return False
    
    return True

def check_api_keys():
    """Check if all required API keys are set"""
    required_keys = [
        'GEMINI_API_KEY',
        'OPENAI_API_KEY', 
        'ELEVENLABS_API_KEY',
        'SUPABASE_KEY'
    ]
    
    missing_keys = []
    for key in required_keys:
        if not os.getenv(key) or os.getenv(key) == f'your_{key.lower()}_here':
            missing_keys.append(key)
    
    if missing_keys:
        print("❌ Missing required API keys:")
        for key in missing_keys:
            print(f"   - {key}")
        print("\nSet them as environment variables or update the scripts")
        return False
    
    return True

def run_script(script_name: str, description: str) -> bool:
    """Run a Python script and return success status"""
    print(f"\n🚀 {description}")
    print("=" * 60)
    
    try:
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, text=True, timeout=1800)  # 30 min timeout
        
        if result.returncode == 0:
            print(result.stdout)
            print(f"✅ {description} completed successfully!")
            return True
        else:
            print(f"❌ {description} failed!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ {description} timed out after 30 minutes")
        return False
    except Exception as e:
        print(f"❌ {description} failed with error: {e}")
        return False

def main():
    """Main orchestrator function"""
    start_time = datetime.now()
    
    print("🎓 NIRA A1 Lesson Creator - Comprehensive Content Generation")
    print("=" * 70)
    print(f"🎯 Goal: Create 14 new A1 lessons (11 → 25 total)")
    print(f"📊 Each lesson: 25 vocab + 15 conversations + 10 grammar + 5 exercises")
    print(f"🎵 Audio: 4 voice options (Arnold, Sam, Elli, Freya)")
    print(f"🤖 AI: Gemini Flash 2.0 + GPT-4o-mini")
    print(f"⏰ Started: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # Step 1: Check dependencies
    print("\n🔍 Step 1: Checking Dependencies")
    if not check_dependencies():
        print("❌ Please install missing packages and try again")
        return False
    print("✅ All packages installed")
    
    # Step 2: Check API keys
    print("\n🔑 Step 2: Checking API Keys")
    if not check_api_keys():
        print("❌ Please set missing API keys and try again")
        return False
    print("✅ All API keys configured")
    
    # Step 3: Generate content
    print("\n📚 Step 3: Generating Lesson Content")
    if not run_script('content_generator.py', 'AI Content Generation'):
        print("❌ Content generation failed. Stopping.")
        return False
    
    # Step 4: Insert into database
    print("\n💾 Step 4: Inserting into Database")
    if not run_script('database_inserter.py', 'Database Insertion'):
        print("❌ Database insertion failed. Stopping.")
        return False
    
    # Step 5: Generate audio (optional - can be done later)
    print("\n🎵 Step 5: Generating Audio Files")
    print("⚠️  Audio generation takes time. Continue? (y/n): ", end="")
    
    choice = input().lower().strip()
    if choice in ['y', 'yes']:
        if not run_script('audio_generator.py', 'Audio Generation'):
            print("⚠️  Audio generation failed, but lessons are created")
    else:
        print("⏭️  Skipping audio generation (can be done later)")
    
    # Step 6: Update voice configuration
    print("\n🔧 Step 6: Updating Voice Configuration")
    update_voice_config()
    
    # Summary
    end_time = datetime.now()
    duration = end_time - start_time
    
    print("\n🎉 NIRA A1 Lesson Creation Complete!")
    print("=" * 50)
    print(f"⏰ Duration: {duration}")
    print(f"📚 Total A1 Lessons: 25")
    print(f"🎯 Ready for: A2, B1, B2, C1, C2 expansion")
    print(f"🌍 Ready for: Multi-language support")
    print("=" * 50)
    
    return True

def update_voice_config():
    """Update the app's voice configuration with selected voices"""
    
    voice_config_content = '''
// Updated Voice Configuration - User Selected Voices
export const VOICE_CONFIGS = {
    male: {
        arnold: {
            id: "VR6AewLTigWG4xSOukaG",
            name: "Arnold Clear",
            settings: { stability: 0.8, similarity_boost: 0.9, style: 0.1 }
        },
        sam: {
            id: "yoZ06aMxZJJ28mfd3POQ", 
            name: "Sam Natural",
            settings: { stability: 0.6, similarity_boost: 0.8, style: 0.3 }
        }
    },
    female: {
        elli: {
            id: "MF3mGyEYCl7XYWbV9V6O",
            name: "Elli Expressive", 
            settings: { stability: 0.4, similarity_boost: 0.7, style: 0.6 }
        },
        freya: {
            id: "jsCqWAovK2LkecY7zXl4",
            name: "Freya Gentle",
            settings: { stability: 0.7, similarity_boost: 0.85, style: 0.25 }
        }
    }
};

export const DEFAULT_VOICES = {
    male: "arnold",
    female: "elli"
};
'''
    
    try:
        with open('NIRA/Config/VoiceConfig.js', 'w') as f:
            f.write(voice_config_content)
        print("✅ Voice configuration updated")
    except Exception as e:
        print(f"⚠️  Could not update voice config: {e}")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
