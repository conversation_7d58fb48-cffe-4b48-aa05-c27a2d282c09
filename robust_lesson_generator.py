#!/usr/bin/env python3
"""
Robust Lesson Content Generator - Handles JSON parsing issues with Tamil text
"""

import json
import time
import requests
from openai import OpenAI
import google.generativeai as genai

# API Configuration
GEMINI_API_KEY = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
OPENAI_API_KEY = "***********************************************************************************************************************************************************************"
ELEVENLABS_API_KEY = "***************************************************"

# Initialize AI clients
genai.configure(api_key=GEMINI_API_KEY)
openai_client = OpenAI(api_key=OPENAI_API_KEY)
gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')

class RobustLessonGenerator:
    def __init__(self):
        self.generated_lessons = []
        
    def generate_lesson_content_structured(self, topic: str, lesson_number: int) -> dict:
        """Generate lesson content using structured approach to avoid JSON issues"""
        
        print(f"🔄 Generating content for Lesson {lesson_number}: {topic}")
        
        # Generate vocabulary first
        vocabulary = self.generate_vocabulary(topic, lesson_number)
        print(f"   ✅ Generated {len(vocabulary)} vocabulary items")
        
        # Generate conversations
        conversations = self.generate_conversations(topic, lesson_number, vocabulary[:5])
        print(f"   ✅ Generated {len(conversations)} conversations")
        
        # Generate grammar points
        grammar_points = self.generate_grammar_points(topic, lesson_number)
        print(f"   ✅ Generated {len(grammar_points)} grammar points")
        
        # Generate exercises
        exercises = self.generate_exercises(topic, lesson_number, vocabulary[:5])
        print(f"   ✅ Generated {len(exercises)} exercises")
        
        return {
            "title": topic,
            "lesson_number": lesson_number,
            "vocabulary": vocabulary,
            "conversations": conversations,
            "grammar_points": grammar_points,
            "exercises": exercises
        }
    
    def generate_vocabulary(self, topic: str, lesson_number: int) -> list:
        """Generate 25 vocabulary items"""
        
        prompt = f"""
        Generate exactly 25 Tamil vocabulary words for the topic: "{topic}"
        
        For each word, provide:
        1. Tamil word
        2. English translation
        3. Simple Tamil example sentence
        4. Pronunciation guide
        
        Use simple, common words appropriate for A1 beginners.
        Use authentic Chennai Tamil dialect.
        
        Format each word as:
        Word: [tamil_word]
        Translation: [english_translation]
        Example: [tamil_example]
        Pronunciation: [phonetic_guide]
        ---
        
        Generate exactly 25 words.
        """
        
        try:
            response = openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=3000
            )
            
            content = response.choices[0].message.content
            return self.parse_vocabulary_response(content, lesson_number)
            
        except Exception as e:
            print(f"❌ Vocabulary generation failed: {e}")
            return self.create_fallback_vocabulary(topic, lesson_number)
    
    def parse_vocabulary_response(self, content: str, lesson_number: int) -> list:
        """Parse vocabulary response into structured format"""
        
        vocabulary = []
        sections = content.split('---')
        
        for i, section in enumerate(sections[:25], 1):
            lines = section.strip().split('\n')
            
            word = ""
            translation = ""
            example = ""
            pronunciation = ""
            
            for line in lines:
                line = line.strip()
                if line.startswith('Word:'):
                    word = line.replace('Word:', '').strip()
                elif line.startswith('Translation:'):
                    translation = line.replace('Translation:', '').strip()
                elif line.startswith('Example:'):
                    example = line.replace('Example:', '').strip()
                elif line.startswith('Pronunciation:'):
                    pronunciation = line.replace('Pronunciation:', '').strip()
            
            if word and translation:
                vocab_item = {
                    "word": word,
                    "translation": translation,
                    "example": example or f"{word} example sentence",
                    "pronunciation": pronunciation or word,
                    "part_of_speech": "noun",
                    "difficulty": "basic",
                    "word_audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_number}_vocab_{i}_word.mp3",
                    "example_audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_number}_vocab_{i}_example.mp3"
                }
                vocabulary.append(vocab_item)
        
        # Ensure we have exactly 25 items
        while len(vocabulary) < 25:
            vocabulary.append(self.create_fallback_vocab_item(len(vocabulary) + 1, lesson_number))
        
        return vocabulary[:25]
    
    def generate_conversations(self, topic: str, lesson_number: int, sample_vocab: list) -> list:
        """Generate 15 conversations"""
        
        vocab_words = [v['word'] for v in sample_vocab] if sample_vocab else []
        
        prompt = f"""
        Create 15 realistic Tamil conversations for the topic: "{topic}"
        
        Use these vocabulary words when possible: {', '.join(vocab_words)}
        
        Each conversation should have:
        - A clear title
        - 2-4 exchanges between speakers
        - Realistic scenarios
        - Simple A1 level Tamil
        
        Format each conversation as:
        CONVERSATION [number]: [title]
        Speaker A: [tamil_text] | [english_translation]
        Speaker B: [tamil_text] | [english_translation]
        Speaker A: [tamil_text] | [english_translation]
        ---
        
        Create exactly 15 conversations.
        """
        
        try:
            response = openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=3000
            )
            
            content = response.choices[0].message.content
            return self.parse_conversations_response(content, lesson_number)
            
        except Exception as e:
            print(f"❌ Conversation generation failed: {e}")
            return self.create_fallback_conversations(topic, lesson_number)
    
    def parse_conversations_response(self, content: str, lesson_number: int) -> list:
        """Parse conversations response into structured format"""
        
        conversations = []
        sections = content.split('---')
        
        for i, section in enumerate(sections[:15], 1):
            lines = section.strip().split('\n')
            
            title = f"Conversation {i}"
            exchanges = []
            
            for line in lines:
                line = line.strip()
                if line.startswith('CONVERSATION'):
                    title = line.split(':', 1)[1].strip() if ':' in line else f"Conversation {i}"
                elif '|' in line and (':' in line):
                    # Parse speaker line
                    speaker_part, rest = line.split(':', 1)
                    speaker = speaker_part.strip()
                    
                    if '|' in rest:
                        tamil_text, english_text = rest.split('|', 1)
                        tamil_text = tamil_text.strip()
                        english_text = english_text.strip()
                        
                        exchange = {
                            "text": tamil_text,
                            "speaker": speaker,
                            "translation": english_text,
                            "pronunciation": tamil_text,
                            "audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_number}_conv_{i}_{len(exchanges)+1}.mp3"
                        }
                        exchanges.append(exchange)
            
            if exchanges:
                conversation = {
                    "title": title,
                    "scenario": f"Practical {title.lower()} scenario",
                    "difficulty": "beginner",
                    "exchanges": exchanges
                }
                conversations.append(conversation)
        
        # Ensure we have exactly 15 conversations
        while len(conversations) < 15:
            conversations.append(self.create_fallback_conversation(len(conversations) + 1, lesson_number))
        
        return conversations[:15]
    
    def generate_grammar_points(self, topic: str, lesson_number: int) -> list:
        """Generate 10 grammar points"""
        
        prompt = f"""
        Create 10 Tamil grammar points relevant to the topic: "{topic}"
        
        Each grammar point should include:
        - Grammar rule name
        - Clear explanation in English
        - 3 Tamil examples
        - Learning tip
        
        Format each as:
        GRAMMAR [number]: [rule_name]
        Explanation: [clear_explanation]
        Examples: [example1] | [example2] | [example3]
        Tip: [helpful_tip]
        ---
        
        Create exactly 10 grammar points.
        """
        
        try:
            response = openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=2000
            )
            
            content = response.choices[0].message.content
            return self.parse_grammar_response(content)
            
        except Exception as e:
            print(f"❌ Grammar generation failed: {e}")
            return self.create_fallback_grammar(topic)
    
    def parse_grammar_response(self, content: str) -> list:
        """Parse grammar response into structured format"""
        
        grammar_points = []
        sections = content.split('---')
        
        for section in sections[:10]:
            lines = section.strip().split('\n')
            
            rule = ""
            explanation = ""
            examples = []
            tip = ""
            
            for line in lines:
                line = line.strip()
                if line.startswith('GRAMMAR'):
                    rule = line.split(':', 1)[1].strip() if ':' in line else "Grammar Rule"
                elif line.startswith('Explanation:'):
                    explanation = line.replace('Explanation:', '').strip()
                elif line.startswith('Examples:'):
                    examples_text = line.replace('Examples:', '').strip()
                    examples = [ex.strip() for ex in examples_text.split('|')]
                elif line.startswith('Tip:'):
                    tip = line.replace('Tip:', '').strip()
            
            if rule and explanation:
                grammar_point = {
                    "rule": rule,
                    "explanation": explanation,
                    "examples": examples or ["Example 1", "Example 2", "Example 3"],
                    "practice_tip": tip or "Practice regularly",
                    "difficulty": "basic",
                    "related_vocabulary": []
                }
                grammar_points.append(grammar_point)
        
        # Ensure we have exactly 10 grammar points
        while len(grammar_points) < 10:
            grammar_points.append(self.create_fallback_grammar_point(len(grammar_points) + 1))
        
        return grammar_points[:10]

    def generate_exercises(self, topic: str, lesson_number: int, sample_vocab: list) -> list:
        """Generate 5 practice exercises"""

        vocab_words = [v['word'] for v in sample_vocab] if sample_vocab else []

        prompt = f"""
        Create 5 practice exercises for Tamil A1 topic: "{topic}"

        Use these vocabulary words: {', '.join(vocab_words)}

        Create these exercise types:
        1. Multiple choice
        2. Fill in the blank
        3. Matching
        4. Translation
        5. Listening comprehension

        Format each as:
        EXERCISE [number]: [type]
        Question: [question_text]
        Tamil Question: [tamil_question_if_applicable]
        Options: [option1] | [option2] | [option3] | [option4]
        Answer: [correct_answer]
        Explanation: [why_correct]
        ---

        Create exactly 5 exercises.
        """

        try:
            response = openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=2000
            )

            content = response.choices[0].message.content
            return self.parse_exercises_response(content, lesson_number)

        except Exception as e:
            print(f"❌ Exercise generation failed: {e}")
            return self.create_fallback_exercises(topic, lesson_number)

    def parse_exercises_response(self, content: str, lesson_number: int) -> list:
        """Parse exercises response into structured format"""

        exercises = []
        sections = content.split('---')

        for i, section in enumerate(sections[:5], 1):
            lines = section.strip().split('\n')

            exercise_type = "multiple_choice"
            question = ""
            tamil_question = ""
            options = []
            answer = ""
            explanation = ""

            for line in lines:
                line = line.strip()
                if line.startswith('EXERCISE'):
                    type_part = line.split(':', 1)[1].strip() if ':' in line else "multiple_choice"
                    exercise_type = type_part.lower().replace(' ', '_')
                elif line.startswith('Question:'):
                    question = line.replace('Question:', '').strip()
                elif line.startswith('Tamil Question:'):
                    tamil_question = line.replace('Tamil Question:', '').strip()
                elif line.startswith('Options:'):
                    options_text = line.replace('Options:', '').strip()
                    options = [opt.strip() for opt in options_text.split('|')]
                elif line.startswith('Answer:'):
                    answer = line.replace('Answer:', '').strip()
                elif line.startswith('Explanation:'):
                    explanation = line.replace('Explanation:', '').strip()

            if question:
                exercise = {
                    "type": exercise_type,
                    "question": question,
                    "question_tamil": tamil_question or question,
                    "options": options or ["Option 1", "Option 2", "Option 3", "Option 4"],
                    "correct_answer": answer or options[0] if options else "Option 1",
                    "explanation": explanation or "Correct answer explanation",
                    "audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_number}_exercise_{i}.mp3"
                }
                exercises.append(exercise)

        # Ensure we have exactly 5 exercises
        while len(exercises) < 5:
            exercises.append(self.create_fallback_exercise(len(exercises) + 1, lesson_number))

        return exercises[:5]

    # Fallback creation methods
    def create_fallback_vocabulary(self, topic: str, lesson_number: int) -> list:
        """Create fallback vocabulary items"""
        basic_words = [
            ("வணக்கம்", "Hello"), ("நன்றி", "Thank you"), ("மன்னிக்கவும்", "Sorry"),
            ("ஆம்", "Yes"), ("இல்லை", "No"), ("தண்ணீர்", "Water"), ("சாப்பாடு", "Food"),
            ("வீடு", "House"), ("பள்ளி", "School"), ("நண்பர்", "Friend"), ("குடும்பம்", "Family"),
            ("அம்மா", "Mother"), ("அப்பா", "Father"), ("பெயர்", "Name"), ("வயது", "Age"),
            ("நேரம்", "Time"), ("பணம்", "Money"), ("வேலை", "Work"), ("புத்தகம்", "Book"),
            ("கார்", "Car"), ("பஸ்", "Bus"), ("ரயில்", "Train"), ("விமானம்", "Airplane"),
            ("மருத்துவர்", "Doctor"), ("ஆசிரியர்", "Teacher")
        ]

        vocabulary = []
        for i, (word, translation) in enumerate(basic_words[:25], 1):
            vocab_item = {
                "word": word,
                "translation": translation,
                "example": f"{word} example sentence",
                "pronunciation": word,
                "part_of_speech": "noun",
                "difficulty": "basic",
                "word_audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_number}_vocab_{i}_word.mp3",
                "example_audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_number}_vocab_{i}_example.mp3"
            }
            vocabulary.append(vocab_item)

        return vocabulary

    def create_fallback_vocab_item(self, index: int, lesson_number: int) -> dict:
        """Create a single fallback vocabulary item"""
        return {
            "word": f"வார்த்தை{index}",
            "translation": f"Word{index}",
            "example": f"வார்த்தை{index} example sentence",
            "pronunciation": f"vaarthai{index}",
            "part_of_speech": "noun",
            "difficulty": "basic",
            "word_audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_number}_vocab_{index}_word.mp3",
            "example_audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_number}_vocab_{index}_example.mp3"
        }

    def create_fallback_conversations(self, topic: str, lesson_number: int) -> list:
        """Create fallback conversations"""
        conversations = []
        for i in range(1, 16):
            conversation = {
                "title": f"Basic Conversation {i}",
                "scenario": f"Simple {topic.lower()} conversation",
                "difficulty": "beginner",
                "exchanges": [
                    {
                        "text": "வணக்கம்",
                        "speaker": "Person A",
                        "translation": "Hello",
                        "pronunciation": "vanakkam",
                        "audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_number}_conv_{i}_1.mp3"
                    },
                    {
                        "text": "வணக்கம், எப்படி இருக்கிறீர்கள்?",
                        "speaker": "Person B",
                        "translation": "Hello, how are you?",
                        "pronunciation": "vanakkam, eppadi irukkireerkal?",
                        "audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_number}_conv_{i}_2.mp3"
                    }
                ]
            }
            conversations.append(conversation)
        return conversations

    def create_fallback_conversation(self, index: int, lesson_number: int) -> dict:
        """Create a single fallback conversation"""
        return {
            "title": f"Conversation {index}",
            "scenario": f"Basic conversation scenario {index}",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": "வணக்கம்",
                    "speaker": "Person A",
                    "translation": "Hello",
                    "pronunciation": "vanakkam",
                    "audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_number}_conv_{index}_1.mp3"
                }
            ]
        }

    def create_fallback_grammar(self, topic: str) -> list:
        """Create fallback grammar points"""
        grammar_points = []
        basic_grammar = [
            ("Basic Sentence Structure", "Tamil follows Subject-Object-Verb order"),
            ("Pronouns", "நான் (I), நீ (you), அவர் (he/she)"),
            ("Present Tense", "Add கிறேன்/கிறாய்/கிறார் for present actions"),
            ("Question Formation", "Add ஆ? at the end for yes/no questions"),
            ("Negation", "Add இல்லை for negative sentences"),
            ("Plural Formation", "Add கள் to make words plural"),
            ("Possessive", "Add உடைய to show possession"),
            ("Time Expressions", "Use இல் after time words"),
            ("Location Words", "Use இல் for 'in', மேல் for 'on'"),
            ("Adjectives", "Adjectives come before nouns in Tamil")
        ]

        for i, (rule, explanation) in enumerate(basic_grammar[:10], 1):
            grammar_point = {
                "rule": rule,
                "explanation": explanation,
                "examples": ["Example 1", "Example 2", "Example 3"],
                "practice_tip": "Practice with simple sentences",
                "difficulty": "basic",
                "related_vocabulary": []
            }
            grammar_points.append(grammar_point)

        return grammar_points

    def create_fallback_grammar_point(self, index: int) -> dict:
        """Create a single fallback grammar point"""
        return {
            "rule": f"Grammar Rule {index}",
            "explanation": f"Basic grammar explanation {index}",
            "examples": ["Example 1", "Example 2", "Example 3"],
            "practice_tip": "Practice regularly",
            "difficulty": "basic",
            "related_vocabulary": []
        }

    def create_fallback_exercises(self, topic: str, lesson_number: int) -> list:
        """Create fallback exercises"""
        exercises = []
        exercise_types = ["multiple_choice", "fill_in_blank", "matching", "translation", "listening"]

        for i, ex_type in enumerate(exercise_types, 1):
            exercise = {
                "type": ex_type,
                "question": f"Basic {ex_type.replace('_', ' ')} question {i}",
                "question_tamil": f"அடிப்படை கேள்வி {i}",
                "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
                "correct_answer": "Option 1",
                "explanation": "This is the correct answer",
                "audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_number}_exercise_{i}.mp3"
            }
            exercises.append(exercise)

        return exercises

    def create_fallback_exercise(self, index: int, lesson_number: int) -> dict:
        """Create a single fallback exercise"""
        return {
            "type": "multiple_choice",
            "question": f"Basic question {index}",
            "question_tamil": f"அடிப்படை கேள்வி {index}",
            "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
            "correct_answer": "Option 1",
            "explanation": "This is the correct answer",
            "audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_number}_exercise_{index}.mp3"
        }

def test_robust_generation():
    """Test the robust lesson generator"""

    print("🧪 TESTING ROBUST LESSON GENERATION")
    print("=" * 50)

    generator = RobustLessonGenerator()

    # Test with Lesson 1
    topic = "Basic Greetings and Introductions"
    lesson_number = 1

    print(f"📖 Testing Lesson {lesson_number}: {topic}")
    print("-" * 40)

    try:
        lesson_data = generator.generate_lesson_content_structured(topic, lesson_number)

        # Count content
        vocab_count = len(lesson_data.get('vocabulary', []))
        conv_count = len(lesson_data.get('conversations', []))
        grammar_count = len(lesson_data.get('grammar_points', []))
        exercise_count = len(lesson_data.get('exercises', []))

        print(f"\n📊 FINAL CONTENT COUNT:")
        print(f"   • Vocabulary: {vocab_count}/25 ✅" if vocab_count >= 20 else f"   • Vocabulary: {vocab_count}/25 ⚠️")
        print(f"   • Conversations: {conv_count}/15 ✅" if conv_count >= 12 else f"   • Conversations: {conv_count}/15 ⚠️")
        print(f"   • Grammar: {grammar_count}/10 ✅" if grammar_count >= 8 else f"   • Grammar: {grammar_count}/10 ⚠️")
        print(f"   • Exercises: {exercise_count}/5 ✅" if exercise_count >= 4 else f"   • Exercises: {exercise_count}/5 ⚠️")

        # Save test lesson
        with open('robust_test_lesson.json', 'w', encoding='utf-8') as f:
            json.dump(lesson_data, f, ensure_ascii=False, indent=2)

        print(f"\n💾 Test lesson saved to: robust_test_lesson.json")

        # Show sample content
        print(f"\n📝 SAMPLE CONTENT:")
        if lesson_data.get('vocabulary'):
            vocab = lesson_data['vocabulary'][0]
            print(f"   Vocab: {vocab.get('word')} = {vocab.get('translation')}")

        if lesson_data.get('conversations'):
            conv = lesson_data['conversations'][0]
            print(f"   Conversation: {conv.get('title')}")

        if lesson_data.get('grammar_points'):
            grammar = lesson_data['grammar_points'][0]
            print(f"   Grammar: {grammar.get('rule')}")

        if lesson_data.get('exercises'):
            exercise = lesson_data['exercises'][0]
            print(f"   Exercise: {exercise.get('type')} - {exercise.get('question')}")

        success = (vocab_count >= 20 and conv_count >= 12 and
                  grammar_count >= 8 and exercise_count >= 4)

        print(f"\n🎯 TEST RESULT: {'✅ SUCCESS' if success else '⚠️ PARTIAL SUCCESS'}")

        return success

    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    test_robust_generation()
