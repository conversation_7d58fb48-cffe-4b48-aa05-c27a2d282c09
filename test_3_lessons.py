#!/usr/bin/env python3
"""
Test script to generate 3 comprehensive lessons with audio
"""

import json
import time
import os
from comprehensive_all_lessons import ComprehensiveAllLessonsGenerator

def test_3_lessons():
    """Test comprehensive generation for first 3 lessons"""
    
    print("🧪 TESTING 3 COMPREHENSIVE LESSONS")
    print("=" * 50)
    print("📚 Testing first 3 lessons with full content + audio")
    print("🎯 Target: 25 vocab + 15 conversations + 10 grammar + 5 exercises each")
    print("🎵 Audio: Arnold voice (Chennai Tamil)")
    print("=" * 50)
    
    # Override lesson topics to just first 3
    test_topics = [
        "Basic Greetings and Introductions",
        "Family Members and Relationships", 
        "Numbers and Counting"
    ]
    
    generator = ComprehensiveAllLessonsGenerator()
    
    # Override the lesson topics for testing
    original_topics = generator.__class__.__module__
    
    start_time = time.time()
    
    for i, topic in enumerate(test_topics, 1):
        print(f"\n📖 Processing Test Lesson {i}: {topic}")
        print("-" * 50)
        
        try:
            # Generate comprehensive content
            lesson_data = generator.generator.generate_lesson_content_structured(topic, i)
            
            if lesson_data:
                # Count content
                vocab_count = len(lesson_data.get('vocabulary', []))
                conv_count = len(lesson_data.get('conversations', []))
                grammar_count = len(lesson_data.get('grammar_points', []))
                exercise_count = len(lesson_data.get('exercises', []))
                
                print(f"📊 Content: {vocab_count}V + {conv_count}C + {grammar_count}G + {exercise_count}E")
                
                # Generate limited audio (5 vocab + 3 conversations + 2 exercises)
                print(f"🎵 Generating sample audio...")
                audio_count = 0
                
                # Generate 5 vocabulary audio files
                for j, vocab in enumerate(lesson_data.get('vocabulary', [])[:5], 1):
                    if 'word' in vocab:
                        filename = f"test_lesson_{i}_vocab_{j}_word.mp3"
                        if generator.generate_elevenlabs_audio(vocab['word'], filename):
                            audio_count += 1
                            print(f"   ✅ Audio: {vocab['word']}")
                        time.sleep(1)
                
                # Generate 3 conversation audio files
                for conv_idx, conversation in enumerate(lesson_data.get('conversations', [])[:3], 1):
                    if conversation.get('exchanges'):
                        exchange = conversation['exchanges'][0]
                        if 'text' in exchange:
                            filename = f"test_lesson_{i}_conv_{conv_idx}.mp3"
                            if generator.generate_elevenlabs_audio(exchange['text'], filename):
                                audio_count += 1
                                print(f"   ✅ Audio: {exchange['text'][:30]}...")
                            time.sleep(1)
                
                # Generate 2 exercise audio files
                for j, exercise in enumerate(lesson_data.get('exercises', [])[:2], 1):
                    if 'question_tamil' in exercise:
                        filename = f"test_lesson_{i}_exercise_{j}.mp3"
                        if generator.generate_elevenlabs_audio(exercise['question_tamil'], filename):
                            audio_count += 1
                            print(f"   ✅ Audio: Exercise {j}")
                        time.sleep(1)
                
                print(f"🎵 Generated {audio_count} audio files")
                
                # Save lesson data
                lesson_file = f'test_lesson_{i}_{topic.replace(" ", "_").lower()}.json'
                with open(lesson_file, 'w', encoding='utf-8') as f:
                    json.dump(lesson_data, f, ensure_ascii=False, indent=2)
                
                print(f"💾 Saved: {lesson_file}")
                print(f"✅ Test Lesson {i} completed successfully")
                
            else:
                print(f"❌ Test Lesson {i} failed to generate content")
                
        except Exception as e:
            print(f"❌ Test Lesson {i} failed with error: {e}")
    
    processing_time = time.time() - start_time
    
    print(f"\n" + "=" * 50)
    print("🎉 3-LESSON TEST COMPLETE")
    print("=" * 50)
    print(f"⏱️ Processing Time: {processing_time/60:.1f} minutes")
    print(f"📁 Test files saved with 'test_lesson_' prefix")
    print(f"🎵 Audio files saved in 'generated_audio/' directory")
    
    # Check if test was successful
    test_files = [f for f in os.listdir('.') if f.startswith('test_lesson_') and f.endswith('.json')]
    audio_files = [f for f in os.listdir('generated_audio') if f.startswith('test_lesson_')]
    
    print(f"\n📊 TEST RESULTS:")
    print(f"   • Lesson files created: {len(test_files)}/3")
    print(f"   • Audio files created: {len(audio_files)}")
    
    if len(test_files) >= 2:
        print(f"\n✅ TEST SUCCESSFUL!")
        print(f"🚀 Ready to run full generation with all 25 lessons")
        print(f"\nTo run full generation:")
        print(f"python3 comprehensive_all_lessons.py")
        return True
    else:
        print(f"\n⚠️ TEST PARTIALLY SUCCESSFUL")
        print(f"🔧 Fix any issues before running full generation")
        return False

def main():
    """Main test function"""
    
    # Create directories
    os.makedirs('generated_audio', exist_ok=True)
    
    success = test_3_lessons()
    
    print(f"\n" + "=" * 50)
    if success:
        print("🎯 3-LESSON TEST PASSED")
        print("✅ System ready for full 25-lesson generation")
    else:
        print("⚠️ 3-LESSON TEST ISSUES")
        print("🔧 Review and fix before proceeding")
    print("=" * 50)

if __name__ == "__main__":
    main()
