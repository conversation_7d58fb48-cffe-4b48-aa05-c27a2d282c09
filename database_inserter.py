#!/usr/bin/env python3
"""
Database Inserter - Upload generated lessons to Supabase
"""

import json
import os
import requests
from typing import Dict, List, Any

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = os.getenv('SUPABASE_KEY', 'your_supabase_key_here')

class DatabaseInserter:
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
        
    def get_tamil_path_id(self) -> str:
        """Get the Tamil A1 Course path ID"""
        query = """
        SELECT lp.id 
        FROM learning_paths lp 
        JOIN languages lang ON lp.language_id = lang.id 
        WHERE lang.name = 'Tamil' AND lp.name = 'Tamil A1 Course'
        """
        
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/rpc/execute_sql",
            headers=self.headers,
            json={"query": query}
        )
        
        if response.status_code == 200:
            result = response.json()
            if result and len(result) > 0:
                return result[0]['id']
        
        raise Exception("Could not find Tamil A1 Course path ID")
    
    def insert_lesson(self, lesson_data: Dict[str, Any], path_id: str) -> bool:
        """Insert a single lesson into the database"""
        
        lesson_payload = {
            'title': lesson_data['title'],
            'sequence_order': lesson_data['sequence_order'],
            'lesson_type': lesson_data['lesson_type'],
            'path_id': path_id,
            'content_metadata': {
                'vocabulary': lesson_data.get('vocabulary', []),
                'conversations': lesson_data.get('conversations', []),
                'grammar_points': lesson_data.get('grammar_points', []),
                'exercises': lesson_data.get('exercises', [])
            },
            'is_active': True,
            'created_at': 'now()',
            'updated_at': 'now()'
        }
        
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/lessons",
            headers=self.headers,
            json=lesson_payload
        )
        
        if response.status_code in [200, 201]:
            print(f"✅ Inserted: {lesson_data['title']}")
            return True
        else:
            print(f"❌ Failed to insert {lesson_data['title']}: {response.text}")
            return False
    
    def insert_all_lessons(self, lessons_file: str = 'generated_a1_lessons.json'):
        """Insert all generated lessons into the database"""
        
        try:
            # Load generated lessons
            with open(lessons_file, 'r', encoding='utf-8') as f:
                lessons = json.load(f)
            
            # Get Tamil path ID
            path_id = self.get_tamil_path_id()
            print(f"📍 Tamil A1 Course Path ID: {path_id}")
            
            # Insert each lesson
            success_count = 0
            for lesson in lessons:
                if self.insert_lesson(lesson, path_id):
                    success_count += 1
            
            print(f"\n🎉 Database Insertion Complete!")
            print(f"✅ Successfully inserted: {success_count}/{len(lessons)} lessons")
            print(f"🎯 Total A1 lessons now: {11 + success_count}")
            
        except Exception as e:
            print(f"❌ Database insertion failed: {e}")

def main():
    """Main execution function"""
    inserter = DatabaseInserter()
    inserter.insert_all_lessons()

if __name__ == "__main__":
    main()
