#!/usr/bin/env python3
"""
Comprehensive Lesson Content Generator for NIRA
Generates full content for all 25 A1 Tamil lessons:
- 25 vocabulary items per lesson
- 15 guided conversations per lesson  
- 10 grammar points per lesson
- 5 practice exercises per lesson
- Audio generation for all content
"""

import json
import time
import requests
from openai import OpenAI
import google.generativeai as genai

# API Configuration
GEMINI_API_KEY = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
OPENAI_API_KEY = "***********************************************************************************************************************************************************************"
ELEVENLABS_API_KEY = "***************************************************"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Initialize AI clients
genai.configure(api_key=GEMINI_API_KEY)
openai_client = OpenAI(api_key=OPENAI_API_KEY)
gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')

# Supabase headers
supabase_headers = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

# Voice configurations (user approved)
VOICE_CONFIG = {
    "male_voices": ["Arnold", "Sam"],
    "female_voices": ["Elli", "Freya"],
    "default_voice": "Arnold"  # User's preferred voice
}

# All 25 lesson topics
LESSON_TOPICS = [
    "Basic Greetings and Introductions",
    "Family Members and Relationships", 
    "Numbers and Counting",
    "Days, Months, and Time",
    "Colors and Descriptions",
    "Food and Dining",
    "Body Parts and Health",
    "Weather and Seasons",
    "Transportation",
    "Clothing and Shopping",
    "Common Verbs and Actions",
    "Personal Information and Identity",
    "Home and Living Spaces",
    "Daily Routines and Activities", 
    "Shopping and Money",
    "Directions and Locations",
    "Health and Body",
    "Hobbies and Interests",
    "Work and Professions",
    "Education and School",
    "Technology and Communication",
    "Emotions and Feelings",
    "Festivals and Celebrations",
    "Animals and Nature",
    "Travel and Transportation Advanced"
]

class ComprehensiveLessonGenerator:
    def __init__(self):
        self.generated_lessons = []
        self.audio_files_generated = 0
        self.total_content_items = 0
        
    def generate_comprehensive_lesson_content(self, topic: str, lesson_number: int) -> dict:
        """Generate comprehensive lesson content with all required components"""
        
        prompt = f"""
        Create a comprehensive Tamil A1 lesson for: "{topic}" (Lesson {lesson_number})
        
        Generate EXACTLY:
        - 25 vocabulary items (Tamil word, English translation, Tamil example sentence, pronunciation guide)
        - 15 guided conversations (realistic dialogues with 2-4 exchanges each)
        - 10 grammar points (rules, explanations, examples, practice tips)
        - 5 practice exercises (multiple choice, fill-in-blanks, matching, translation, listening)
        
        Use authentic Chennai Tamil dialect. Make content progressive and practical.
        
        Return as valid JSON with this exact structure:
        {{
            "title": "{topic}",
            "lesson_number": {lesson_number},
            "vocabulary": [
                {{
                    "word": "tamil_word",
                    "translation": "english_translation",
                    "example": "tamil_example_sentence",
                    "pronunciation": "phonetic_pronunciation",
                    "part_of_speech": "noun/verb/adjective/etc",
                    "difficulty": "basic/intermediate",
                    "word_audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_number}_vocab_{{index}}_word.mp3",
                    "example_audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_number}_vocab_{{index}}_example.mp3"
                }}
            ],
            "conversations": [
                {{
                    "title": "conversation_title",
                    "scenario": "realistic_scenario_description",
                    "difficulty": "beginner",
                    "exchanges": [
                        {{
                            "text": "tamil_text",
                            "speaker": "Teacher/Student/Person A/Person B",
                            "translation": "english_translation",
                            "pronunciation": "phonetic_guide",
                            "audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_number}_conv_{{conv_index}}_{{exchange_index}}.mp3"
                        }}
                    ]
                }}
            ],
            "grammar_points": [
                {{
                    "rule": "grammar_rule_name",
                    "explanation": "clear_explanation_in_english",
                    "examples": ["tamil_example_1", "tamil_example_2", "tamil_example_3"],
                    "practice_tip": "helpful_learning_tip",
                    "difficulty": "basic/intermediate",
                    "related_vocabulary": ["word1", "word2"]
                }}
            ],
            "exercises": [
                {{
                    "type": "multiple_choice/fill_in_blank/matching/translation/listening",
                    "question": "exercise_question",
                    "question_tamil": "tamil_question_if_applicable",
                    "options": ["option1", "option2", "option3", "option4"],
                    "correct_answer": "correct_option",
                    "explanation": "why_this_is_correct",
                    "audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_number}_exercise_{{index}}.mp3"
                }}
            ]
        }}
        
        Make sure to generate exactly 25 vocabulary, 15 conversations, 10 grammar points, and 5 exercises.
        Focus on practical, everyday Tamil usage with cultural context.
        """
        
        try:
            # Try Gemini first
            response = gemini_model.generate_content(prompt)
            content = response.text
            
            # Clean and parse JSON
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0]
            elif "```" in content:
                content = content.split("```")[1].split("```")[0]
            
            lesson_data = json.loads(content.strip())
            
            # Validate content counts
            vocab_count = len(lesson_data.get('vocabulary', []))
            conv_count = len(lesson_data.get('conversations', []))
            grammar_count = len(lesson_data.get('grammar_points', []))
            exercise_count = len(lesson_data.get('exercises', []))
            
            print(f"✅ Generated: {vocab_count} vocab, {conv_count} conversations, {grammar_count} grammar, {exercise_count} exercises")
            
            # If counts are insufficient, supplement with OpenAI
            if vocab_count < 25 or conv_count < 15 or grammar_count < 10 or exercise_count < 5:
                print(f"⚠️ Insufficient content, supplementing with OpenAI...")
                lesson_data = self.supplement_with_openai(lesson_data, topic, lesson_number)
            
            return lesson_data
            
        except Exception as e:
            print(f"❌ Gemini failed for {topic}: {e}")
            return self.generate_with_openai_fallback(topic, lesson_number)
    
    def supplement_with_openai(self, existing_data: dict, topic: str, lesson_number: int) -> dict:
        """Supplement insufficient content using OpenAI"""
        
        vocab_needed = max(0, 25 - len(existing_data.get('vocabulary', [])))
        conv_needed = max(0, 15 - len(existing_data.get('conversations', [])))
        grammar_needed = max(0, 10 - len(existing_data.get('grammar_points', [])))
        exercise_needed = max(0, 5 - len(existing_data.get('exercises', [])))
        
        if vocab_needed > 0 or conv_needed > 0 or grammar_needed > 0 or exercise_needed > 0:
            supplement_prompt = f"""
            Supplement the existing Tamil A1 lesson content for "{topic}".
            
            Generate additional content to reach these targets:
            - {vocab_needed} more vocabulary items
            - {conv_needed} more conversations  
            - {grammar_needed} more grammar points
            - {exercise_needed} more exercises
            
            Return only the additional content in the same JSON format.
            """
            
            try:
                response = openai_client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[{"role": "user", "content": supplement_prompt}],
                    temperature=0.7,
                    max_tokens=3000
                )
                
                supplement_content = response.choices[0].message.content
                if "```json" in supplement_content:
                    supplement_content = supplement_content.split("```json")[1].split("```")[0]
                
                supplement_data = json.loads(supplement_content.strip())
                
                # Merge supplemental content
                if 'vocabulary' in supplement_data:
                    existing_data.setdefault('vocabulary', []).extend(supplement_data['vocabulary'])
                if 'conversations' in supplement_data:
                    existing_data.setdefault('conversations', []).extend(supplement_data['conversations'])
                if 'grammar_points' in supplement_data:
                    existing_data.setdefault('grammar_points', []).extend(supplement_data['grammar_points'])
                if 'exercises' in supplement_data:
                    existing_data.setdefault('exercises', []).extend(supplement_data['exercises'])
                
                print(f"✅ Supplemented with OpenAI")
                
            except Exception as e:
                print(f"⚠️ OpenAI supplement failed: {e}")
        
        return existing_data
    
    def generate_with_openai_fallback(self, topic: str, lesson_number: int) -> dict:
        """Fallback content generation using OpenAI"""
        
        prompt = f"""
        Create a comprehensive Tamil A1 lesson for: "{topic}" (Lesson {lesson_number})
        
        Generate exactly 25 vocabulary, 15 conversations, 10 grammar points, and 5 exercises.
        Use authentic Chennai Tamil. Return as valid JSON with proper structure.
        """
        
        try:
            response = openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=4000
            )
            
            content = response.choices[0].message.content
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0]
            
            return json.loads(content.strip())
            
        except Exception as e:
            print(f"❌ OpenAI fallback failed for {topic}: {e}")
            return self.create_minimal_lesson(topic, lesson_number)
    
    def create_minimal_lesson(self, topic: str, lesson_number: int) -> dict:
        """Create minimal lesson structure as last resort"""
        return {
            "title": topic,
            "lesson_number": lesson_number,
            "vocabulary": [],
            "conversations": [],
            "grammar_points": [],
            "exercises": [],
            "status": "minimal_fallback"
        }

    def generate_audio_for_lesson(self, lesson_data: dict) -> int:
        """Generate audio files for all lesson content using ElevenLabs"""
        audio_count = 0
        lesson_number = lesson_data.get('lesson_number', 1)

        print(f"\n🎵 Generating audio for Lesson {lesson_number}: {lesson_data.get('title', 'Unknown')}")

        # Generate vocabulary audio
        for i, vocab in enumerate(lesson_data.get('vocabulary', []), 1):
            # Word audio
            if 'word' in vocab:
                filename = f"lesson_{lesson_number}_vocab_{i}_word.mp3"
                if self.generate_elevenlabs_audio(vocab['word'], filename):
                    audio_count += 1
                time.sleep(1)  # Rate limiting

            # Example audio
            if 'example' in vocab:
                filename = f"lesson_{lesson_number}_vocab_{i}_example.mp3"
                if self.generate_elevenlabs_audio(vocab['example'], filename):
                    audio_count += 1
                time.sleep(1)

        # Generate conversation audio
        for conv_idx, conversation in enumerate(lesson_data.get('conversations', []), 1):
            for ex_idx, exchange in enumerate(conversation.get('exchanges', []), 1):
                if 'text' in exchange:
                    filename = f"lesson_{lesson_number}_conv_{conv_idx}_{ex_idx}.mp3"
                    if self.generate_elevenlabs_audio(exchange['text'], filename):
                        audio_count += 1
                    time.sleep(1)

        # Generate exercise audio
        for i, exercise in enumerate(lesson_data.get('exercises', []), 1):
            if 'question_tamil' in exercise:
                filename = f"lesson_{lesson_number}_exercise_{i}.mp3"
                if self.generate_elevenlabs_audio(exercise['question_tamil'], filename):
                    audio_count += 1
                time.sleep(1)

        print(f"✅ Generated {audio_count} audio files for Lesson {lesson_number}")
        return audio_count

    def generate_elevenlabs_audio(self, text: str, filename: str) -> bool:
        """Generate audio using ElevenLabs API"""
        try:
            # Use Arnold voice (user's preferred)
            voice_id = "pNInz6obpgDQGcFmaJgB"  # Arnold voice ID

            url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"

            headers = {
                "Accept": "audio/mpeg",
                "Content-Type": "application/json",
                "xi-api-key": ELEVENLABS_API_KEY
            }

            data = {
                "text": text,
                "model_id": "eleven_multilingual_v2",
                "voice_settings": {
                    "stability": 0.5,
                    "similarity_boost": 0.75,
                    "style": 0.0,
                    "use_speaker_boost": True
                }
            }

            response = requests.post(url, json=data, headers=headers)

            if response.status_code == 200:
                # Save audio file locally first
                audio_path = f"generated_audio/{filename}"
                with open(audio_path, 'wb') as f:
                    f.write(response.content)

                # Upload to Supabase storage
                return self.upload_audio_to_supabase(audio_path, filename)
            else:
                print(f"❌ ElevenLabs API error for {filename}: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ Audio generation failed for {filename}: {e}")
            return False

    def upload_audio_to_supabase(self, local_path: str, filename: str) -> bool:
        """Upload audio file to Supabase storage"""
        try:
            storage_url = f"{SUPABASE_URL}/storage/v1/object/lesson-audio/tamil/a1/{filename}"

            with open(local_path, 'rb') as f:
                files = {'file': f}
                headers = {
                    'Authorization': f'Bearer {SUPABASE_KEY}',
                    'apikey': SUPABASE_KEY
                }

                response = requests.post(storage_url, files=files, headers=headers)

                if response.status_code in [200, 201]:
                    print(f"✅ Uploaded audio: {filename}")
                    return True
                else:
                    print(f"❌ Upload failed for {filename}: {response.status_code}")
                    return False

        except Exception as e:
            print(f"❌ Upload error for {filename}: {e}")
            return False

    def update_lesson_in_database(self, lesson_data: dict) -> bool:
        """Update lesson content in Supabase database"""
        try:
            lesson_number = lesson_data.get('lesson_number', 1)

            # Get lesson ID
            query_url = f"{SUPABASE_URL}/rest/v1/lessons"
            params = {
                'select': 'id',
                'sequence_order': f'eq.{lesson_number}',
                'path_id': 'eq.6b427613-420f-4586-bce8-2773d722f0b4'  # Tamil A1 path ID
            }

            response = requests.get(query_url, headers=supabase_headers, params=params)

            if response.status_code == 200 and response.json():
                lesson_id = response.json()[0]['id']

                # Update lesson content
                update_url = f"{SUPABASE_URL}/rest/v1/lessons"
                params = {'id': f'eq.{lesson_id}'}

                update_data = {
                    'content_metadata': lesson_data,
                    'updated_at': 'now()'
                }

                response = requests.patch(update_url, json=update_data, headers=supabase_headers, params=params)

                if response.status_code in [200, 204]:
                    print(f"✅ Updated lesson {lesson_number} in database")
                    return True
                else:
                    print(f"❌ Database update failed for lesson {lesson_number}: {response.status_code}")
                    return False
            else:
                print(f"❌ Could not find lesson {lesson_number} in database")
                return False

        except Exception as e:
            print(f"❌ Database update error for lesson {lesson_number}: {e}")
            return False

    def process_all_lessons(self, generate_audio: bool = True) -> dict:
        """Process all 25 lessons with comprehensive content and audio"""

        print("🚀 NIRA COMPREHENSIVE LESSON GENERATOR")
        print("=" * 60)
        print(f"📚 Processing {len(LESSON_TOPICS)} Tamil A1 Lessons")
        print(f"🎯 Target: 25 vocab + 15 conversations + 10 grammar + 5 exercises per lesson")
        print(f"🎵 Audio Generation: {'Enabled' if generate_audio else 'Disabled'}")
        print("=" * 60)

        # Create audio directory
        import os
        os.makedirs('generated_audio', exist_ok=True)

        results = {
            'lessons_processed': 0,
            'lessons_successful': 0,
            'total_vocabulary': 0,
            'total_conversations': 0,
            'total_grammar_points': 0,
            'total_exercises': 0,
            'total_audio_files': 0,
            'failed_lessons': [],
            'processing_time': 0
        }

        start_time = time.time()

        for i, topic in enumerate(LESSON_TOPICS, 1):
            print(f"\n📖 Processing Lesson {i}: {topic}")
            print("-" * 50)

            try:
                # Generate comprehensive content
                lesson_data = self.generate_comprehensive_lesson_content(topic, i)

                if lesson_data and lesson_data.get('status') != 'minimal_fallback':
                    # Count content
                    vocab_count = len(lesson_data.get('vocabulary', []))
                    conv_count = len(lesson_data.get('conversations', []))
                    grammar_count = len(lesson_data.get('grammar_points', []))
                    exercise_count = len(lesson_data.get('exercises', []))

                    results['total_vocabulary'] += vocab_count
                    results['total_conversations'] += conv_count
                    results['total_grammar_points'] += grammar_count
                    results['total_exercises'] += exercise_count

                    print(f"📊 Content: {vocab_count}V + {conv_count}C + {grammar_count}G + {exercise_count}E")

                    # Generate audio if enabled
                    audio_count = 0
                    if generate_audio:
                        audio_count = self.generate_audio_for_lesson(lesson_data)
                        results['total_audio_files'] += audio_count

                    # Update database
                    if self.update_lesson_in_database(lesson_data):
                        results['lessons_successful'] += 1
                        print(f"✅ Lesson {i} completed successfully")
                    else:
                        results['failed_lessons'].append(f"Lesson {i}: Database update failed")
                        print(f"⚠️ Lesson {i} content generated but database update failed")

                    # Save lesson data locally
                    with open(f'lesson_{i}_comprehensive.json', 'w', encoding='utf-8') as f:
                        json.dump(lesson_data, f, ensure_ascii=False, indent=2)

                else:
                    results['failed_lessons'].append(f"Lesson {i}: Content generation failed")
                    print(f"❌ Lesson {i} failed to generate proper content")

                results['lessons_processed'] += 1

                # Rate limiting between lessons
                time.sleep(2)

            except Exception as e:
                results['failed_lessons'].append(f"Lesson {i}: {str(e)}")
                print(f"❌ Lesson {i} failed with error: {e}")
                results['lessons_processed'] += 1

        results['processing_time'] = time.time() - start_time

        # Print final summary
        self.print_final_summary(results)

        return results

    def print_final_summary(self, results: dict):
        """Print comprehensive summary of processing results"""

        print("\n" + "=" * 70)
        print("🎉 COMPREHENSIVE LESSON GENERATION COMPLETE")
        print("=" * 70)

        print(f"📊 PROCESSING SUMMARY:")
        print(f"   • Lessons Processed: {results['lessons_processed']}/25")
        print(f"   • Lessons Successful: {results['lessons_successful']}/25")
        print(f"   • Success Rate: {(results['lessons_successful']/25)*100:.1f}%")
        print(f"   • Processing Time: {results['processing_time']/60:.1f} minutes")

        print(f"\n📚 CONTENT GENERATED:")
        print(f"   • Total Vocabulary Items: {results['total_vocabulary']}")
        print(f"   • Total Conversations: {results['total_conversations']}")
        print(f"   • Total Grammar Points: {results['total_grammar_points']}")
        print(f"   • Total Exercises: {results['total_exercises']}")
        print(f"   • Total Content Items: {results['total_vocabulary'] + results['total_conversations'] + results['total_grammar_points'] + results['total_exercises']}")

        print(f"\n🎵 AUDIO GENERATION:")
        print(f"   • Total Audio Files: {results['total_audio_files']}")
        print(f"   • Estimated Audio Duration: {results['total_audio_files'] * 3:.0f} seconds")

        if results['failed_lessons']:
            print(f"\n⚠️ FAILED LESSONS ({len(results['failed_lessons'])}):")
            for failure in results['failed_lessons']:
                print(f"   • {failure}")

        print(f"\n🏆 ACHIEVEMENT UNLOCKED:")
        print(f"   • Industry-leading content volume: {results['total_vocabulary']} vocabulary items")
        print(f"   • Comprehensive lesson structure: 4-part content system")
        print(f"   • Multi-modal learning: Text + Audio + Exercises")
        print(f"   • Cultural authenticity: Chennai Tamil dialect")
        print(f"   • Scalable architecture: Ready for A2, B1, B2+ expansion")

        print("\n🚀 NIRA IS NOW THE PREMIER TAMIL LEARNING PLATFORM!")
        print("=" * 70)

def main():
    """Main execution function"""
    generator = ComprehensiveLessonGenerator()

    # Process all lessons with audio generation
    results = generator.process_all_lessons(generate_audio=True)

    # Save results summary
    with open('comprehensive_generation_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    print(f"\n💾 Results saved to: comprehensive_generation_results.json")
    print(f"📁 Individual lessons saved as: lesson_X_comprehensive.json")

if __name__ == "__main__":
    main()
