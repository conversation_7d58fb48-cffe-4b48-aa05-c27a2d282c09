#!/usr/bin/env python3
"""
NIRA Content Generator - AI-Powered Language Learning Content Creation
Generates comprehensive lessons for Tamil A1 level using Gemini Flash 2.0 and GPT-4o-mini
"""

import os
import json
import requests
import time
from typing import Dict, List, Any
import google.generativeai as genai
from openai import OpenAI

# Configuration
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', 'your_gemini_key_here')
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', 'your_openai_key_here')
ELEVENLABS_API_KEY = os.getenv('ELEVENLABS_API_KEY', 'your_elevenlabs_key_here')

# Initialize AI clients
genai.configure(api_key=GEMINI_API_KEY)
openai_client = OpenAI(api_key=OPENAI_API_KEY)

# Voice configurations (User selected: <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)
VOICE_CONFIGS = {
    "arnold_male": {
        "voice_id": "VR6AewLTigWG4xSOukaG",
        "name": "Arnold Clear Male",
        "settings": {"stability": 0.8, "similarity_boost": 0.9, "style": 0.1}
    },
    "sam_male": {
        "voice_id": "yoZ06aMxZJJ28mfd3POQ", 
        "name": "Sam Natural Male",
        "settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.3}
    },
    "elli_female": {
        "voice_id": "MF3mGyEYCl7XYWbV9V6O",
        "name": "Elli Expressive Female", 
        "settings": {"stability": 0.4, "similarity_boost": 0.7, "style": 0.6}
    },
    "freya_female": {
        "voice_id": "jsCqWAovK2LkecY7zXl4",
        "name": "Freya Gentle Female",
        "settings": {"stability": 0.7, "similarity_boost": 0.85, "style": 0.25}
    }
}

# A1 Lesson Topics (14 new lessons to reach 25 total)
A1_LESSON_TOPICS = [
    "Personal Information and Identity",
    "Home and Living Spaces", 
    "Daily Routines and Activities",
    "Shopping and Money",
    "Directions and Locations",
    "Health and Body",
    "Hobbies and Interests",
    "Work and Professions",
    "Education and School",
    "Technology and Communication",
    "Emotions and Feelings",
    "Festivals and Celebrations",
    "Animals and Nature",
    "Travel and Transportation Advanced"
]

class ContentGenerator:
    def __init__(self):
        self.gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')
        self.lesson_count = 12  # Starting from lesson 12 (we have 11 already)
        
    def generate_lesson_content(self, topic: str, lesson_number: int) -> Dict[str, Any]:
        """Generate comprehensive lesson content using Gemini Flash 2.0"""
        
        prompt = f"""
        Create a comprehensive Tamil A1 beginner lesson for topic: "{topic}"
        
        Requirements:
        - 25 vocabulary items with Tamil word, English translation, and example sentence in Tamil
        - 15 guided conversation exchanges (realistic dialogues using the vocabulary)
        - 10 essential grammar points related to the topic
        - 5 practice exercises (multiple choice, fill-in-blanks, matching, etc.)
        
        Format as JSON with this exact structure:
        {{
            "title": "{topic}",
            "vocabulary": [
                {{
                    "word": "tamil_word",
                    "translation": "english_translation", 
                    "example": "tamil_example_sentence",
                    "word_audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_{lesson_number}_vocab_{{index}}_word.mp3",
                    "example_audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_{lesson_number}_vocab_{{index}}_example.mp3"
                }}
            ],
            "conversations": [
                {{
                    "title": "conversation_title",
                    "exchanges": [
                        {{
                            "text": "tamil_text",
                            "speaker": "Teacher/Student",
                            "translation": "english_translation"
                        }}
                    ]
                }}
            ],
            "grammar_points": [
                {{
                    "rule": "grammar_rule_name",
                    "explanation": "clear_explanation",
                    "examples": ["tamil_example_1", "tamil_example_2"],
                    "tips": "helpful_learning_tip"
                }}
            ],
            "exercises": [
                {{
                    "type": "multiple_choice/fill_blank/matching/translation",
                    "question": "tamil_question",
                    "options": ["option1", "option2", "option3", "option4"],
                    "correct_answer": "correct_option"
                }}
            ]
        }}
        
        Focus on:
        - Authentic Chennai Tamil dialect
        - Practical, everyday usage
        - Progressive difficulty within A1 level
        - Cultural context and relevance
        - Clear pronunciation guides
        """
        
        try:
            response = self.gemini_model.generate_content(prompt)
            content = response.text
            
            # Clean up the response to extract JSON
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0]
            elif "```" in content:
                content = content.split("```")[1].split("```")[0]
                
            return json.loads(content.strip())
            
        except Exception as e:
            print(f"❌ Gemini generation failed for {topic}: {e}")
            return self.generate_with_openai(topic, lesson_number)
    
    def generate_with_openai(self, topic: str, lesson_number: int) -> Dict[str, Any]:
        """Fallback content generation using OpenAI GPT-4o-mini"""
        
        prompt = f"""
        Create a comprehensive Tamil A1 beginner lesson for: "{topic}"
        
        Generate exactly:
        - 25 vocabulary items (Tamil word, English translation, Tamil example)
        - 15 conversation exchanges (realistic dialogues)
        - 10 grammar points (rules, explanations, examples)
        - 5 practice exercises (varied types)
        
        Return as valid JSON matching this structure:
        {{
            "title": "{topic}",
            "vocabulary": [25 items with word, translation, example, audio URLs],
            "conversations": [conversation objects with exchanges],
            "grammar_points": [10 grammar rules with explanations],
            "exercises": [5 varied exercise types]
        }}
        
        Focus on authentic Chennai Tamil for A1 beginners.
        """
        
        try:
            response = openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=4000
            )
            
            content = response.choices[0].message.content
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0]
            elif "```" in content:
                content = content.split("```")[1].split("```")[0]
                
            return json.loads(content.strip())
            
        except Exception as e:
            print(f"❌ OpenAI generation failed for {topic}: {e}")
            return None

def main():
    """Generate all 14 new A1 lessons"""
    generator = ContentGenerator()
    
    print("🚀 NIRA Content Generator - Creating 14 New A1 Lessons")
    print("=" * 60)
    
    generated_lessons = []
    
    for i, topic in enumerate(A1_LESSON_TOPICS, start=12):
        print(f"\n📚 Generating Lesson {i}: {topic}")
        print("-" * 40)
        
        lesson_content = generator.generate_lesson_content(topic, i)
        
        if lesson_content:
            lesson_content['sequence_order'] = i
            lesson_content['lesson_type'] = 'vocabulary'
            generated_lessons.append(lesson_content)
            print(f"✅ Generated: {len(lesson_content.get('vocabulary', []))} vocab, "
                  f"{len(lesson_content.get('conversations', []))} conversations, "
                  f"{len(lesson_content.get('grammar_points', []))} grammar, "
                  f"{len(lesson_content.get('exercises', []))} exercises")
        else:
            print(f"❌ Failed to generate content for {topic}")
        
        # Rate limiting
        time.sleep(2)
    
    # Save generated content
    output_file = 'generated_a1_lessons.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(generated_lessons, f, ensure_ascii=False, indent=2)
    
    print(f"\n🎉 Content Generation Complete!")
    print(f"📁 Saved to: {output_file}")
    print(f"📊 Generated {len(generated_lessons)} lessons")
    print(f"🎯 Total A1 lessons will be: {11 + len(generated_lessons)}")

if __name__ == "__main__":
    main()
