#!/usr/bin/env python3
"""
Update Lesson Content with Audio URLs
This script updates the Animals and Nature lesson content_metadata with generated audio URLs
"""

import json
import requests

# Supabase Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0NzI4NzQsImV4cCI6MjA1MDA0ODg3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"

# Lesson ID for Animals and Nature
LESSON_ID = "b966c742-d36d-4d94-9e35-7c17a5039487"

def fetch_current_lesson():
    """Fetch current lesson content from Supabase"""
    try:
        print("📥 Fetching current lesson content...")
        
        query_url = f"{SUPABASE_URL}/rest/v1/lessons"
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json"
        }
        
        params = {
            "id": f"eq.{LESSON_ID}",
            "select": "id,title,content_metadata"
        }
        
        response = requests.get(query_url, headers=headers, params=params)
        
        if response.status_code == 200:
            lessons = response.json()
            if lessons and len(lessons) > 0:
                lesson = lessons[0]
                print(f"✅ Fetched lesson: {lesson['title']}")
                return lesson['content_metadata']
            else:
                print("❌ No lesson found with that ID")
                return None
        else:
            print(f"❌ Failed to fetch lesson: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error fetching lesson: {e}")
        return None

def update_content_with_audio_urls(content_metadata, audio_urls):
    """Update content metadata with audio URLs"""
    updated_content = content_metadata.copy()
    
    print(f"🔗 Updating content with {len(audio_urls)} audio URLs...")
    
    for path, url in audio_urls.items():
        print(f"   Adding: {path} -> {url}")
        
        # Parse the path and update the content
        path_parts = path.split('.')
        
        try:
            if path_parts[0] == "vocabulary":
                vocab_index = int(path_parts[1])
                field = path_parts[2]  # word_audio_url or example_audio_url
                
                if vocab_index < len(updated_content.get("vocabulary", [])):
                    updated_content["vocabulary"][vocab_index][field] = url
                    
            elif path_parts[0] == "conversations":
                conv_index = int(path_parts[1])
                ex_index = int(path_parts[3])
                
                if (conv_index < len(updated_content.get("conversations", [])) and
                    ex_index < len(updated_content["conversations"][conv_index].get("exchanges", []))):
                    updated_content["conversations"][conv_index]["exchanges"][ex_index]["audio_url"] = url
                    
            elif path_parts[0] == "grammar_points":
                gram_index = int(path_parts[1])
                ex_index = int(path_parts[3])
                
                if gram_index < len(updated_content.get("grammar_points", [])):
                    if "examples_audio_urls" not in updated_content["grammar_points"][gram_index]:
                        updated_content["grammar_points"][gram_index]["examples_audio_urls"] = []
                    
                    # Ensure the list is long enough
                    while len(updated_content["grammar_points"][gram_index]["examples_audio_urls"]) <= ex_index:
                        updated_content["grammar_points"][gram_index]["examples_audio_urls"].append("")
                    
                    updated_content["grammar_points"][gram_index]["examples_audio_urls"][ex_index] = url
                    
            elif path_parts[0] == "exercises":
                ex_index = int(path_parts[1])
                opt_index = int(path_parts[3])
                
                if ex_index < len(updated_content.get("exercises", [])):
                    if "options_audio_urls" not in updated_content["exercises"][ex_index]:
                        updated_content["exercises"][ex_index]["options_audio_urls"] = []
                    
                    # Ensure the list is long enough
                    while len(updated_content["exercises"][ex_index]["options_audio_urls"]) <= opt_index:
                        updated_content["exercises"][ex_index]["options_audio_urls"].append("")
                    
                    updated_content["exercises"][ex_index]["options_audio_urls"][opt_index] = url
                    
        except (IndexError, ValueError, KeyError) as e:
            print(f"❌ Failed to update path {path}: {e}")
    
    return updated_content

def update_lesson_in_database(lesson_id, updated_content):
    """Update lesson content_metadata in Supabase"""
    try:
        print(f"📝 Updating lesson in database...")
        
        update_url = f"{SUPABASE_URL}/rest/v1/lessons"
        headers = {
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json",
            "apikey": SUPABASE_ANON_KEY,
            "Prefer": "return=minimal"
        }
        
        data = {
            "content_metadata": updated_content,
            "has_audio": True
        }
        
        response = requests.patch(
            f"{update_url}?id=eq.{lesson_id}", 
            json=data, 
            headers=headers
        )
        
        if response.status_code in [200, 204]:
            print(f"✅ Successfully updated lesson content")
            return True
        else:
            print(f"❌ Update failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Update error: {e}")
        return False

def main():
    """Main function to update lesson with audio URLs"""
    
    # Check if audio URLs file exists
    try:
        with open("generated_audio_urls.json", "r") as f:
            audio_urls = json.load(f)
        print(f"📁 Loaded {len(audio_urls)} audio URLs from generated_audio_urls.json")
    except FileNotFoundError:
        print("❌ generated_audio_urls.json not found!")
        print("Please run generate_animals_audio.py first to generate audio files and URLs")
        return
    except json.JSONDecodeError:
        print("❌ Invalid JSON in generated_audio_urls.json")
        return
    
    if not audio_urls:
        print("❌ No audio URLs found in the file")
        return
    
    # Fetch current lesson content
    current_content = fetch_current_lesson()
    if not current_content:
        return
    
    # Update content with audio URLs
    updated_content = update_content_with_audio_urls(current_content, audio_urls)
    
    # Update lesson in database
    if update_lesson_in_database(LESSON_ID, updated_content):
        print(f"\n🎉 Successfully updated Animals and Nature lesson with audio URLs!")
        print(f"📊 Updated {len(audio_urls)} audio references")
        print(f"\n📋 Next steps:")
        print(f"1. Test audio playback in the NIRA app")
        print(f"2. Verify caching works correctly")
        print(f"3. Check that all audio buttons work")
    else:
        print(f"\n❌ Failed to update lesson in database")

if __name__ == "__main__":
    main()
