#!/usr/bin/env python3
"""
Generate Audio for Animals and Nature Lesson
This script generates audio for all Tamil content in the Animals and Nature lesson
"""

import os
import requests
import time
import json
from pathlib import Path

# IMPORTANT: Replace with real ElevenLabs API key
ELEVENLABS_API_KEY = "YOUR_REAL_ELEVENLABS_API_KEY_HERE"

# Voice ID for Tamil content (use a clear, neutral voice)
VOICE_ID = "pNInz6obpgDQGcFmaJgB"  # Adam voice - clear pronunciation

# Animals and Nature lesson content
LESSON_CONTENT = {
    "vocabulary": [
        {"word": "நாய்", "example": "நாய் குரைக்கிறது"},
        {"word": "பூனை", "example": "பூனை பால் குடிக்கிறது"},
        {"word": "பறவை", "example": "பறவை பறக்கிறது"},
        {"word": "மரம்", "example": "பெரிய மரம் இருக்கிறது"},
        {"word": "பூ", "example": "அழகான பூ மலர்ந்திருக்கிறது"},
        {"word": "இலை", "example": "பச்சை இலை விழுந்தது"},
        {"word": "புல்", "example": "பச்சை புல் வளர்கிறது"},
        {"word": "மீன்", "example": "மீன் நீரில் நீந்துகிறது"},
        {"word": "யானை", "example": "பெரிய யானை நடக்கிறது"},
        {"word": "சிங்கம்", "example": "சிங்கம் கர்ஜிக்கிறது"},
        {"word": "குதிரை", "example": "குதிரை ஓடுகிறது"},
        {"word": "பசு", "example": "பசு பால் கொடுக்கிறது"},
        {"word": "ஆடு", "example": "ஆடு புல் தின்கிறது"},
        {"word": "கோழி", "example": "கோழி முட்டை இடுகிறது"},
        {"word": "வாத்து", "example": "வாத்து நீரில் நீந்துகிறது"},
        {"word": "முயல்", "example": "முயல் வேகமாக ஓடுகிறது"},
        {"word": "எலி", "example": "சிறிய எலி ஓடுகிறது"},
        {"word": "பாம்பு", "example": "பாம்பு ஊர்ந்து செல்கிறது"},
        {"word": "தவளை", "example": "தவளை குதிக்கிறது"},
        {"word": "வண்ணத்துப்பூச்சி", "example": "அழகான வண்ணத்துப்பூச்சி பறக்கிறது"},
        {"word": "தேனீ", "example": "தேனீ தேன் சேகரிக்கிறது"},
        {"word": "எறும்பு", "example": "எறும்பு வேலை செய்கிறது"},
        {"word": "சூரியன்", "example": "சூரியன் பிரகாசிக்கிறது"},
        {"word": "நிலா", "example": "நிலா ஒளிர்கிறது"},
        {"word": "நட்சத்திரம்", "example": "நட்சத்திரம் மின்னுகிறது"}
    ],
    "conversations": [
        {"text": "இது என்ன விலங்கு?", "translation": "What animal is this?"},
        {"text": "இது யானை", "translation": "This is an elephant"},
        {"text": "இந்த பூ அழகாக இருக்கிறது", "translation": "This flower is beautiful"},
        {"text": "ஆம், மிகவும் அழகு", "translation": "Yes, very beautiful"},
        {"text": "நாய் குரைக்கிறது", "translation": "The dog is barking"},
        {"text": "ஆம், அது பசியாக இருக்கும்", "translation": "Yes, it must be hungry"}
    ],
    "grammar_examples": [
        "நாய் குரைக்கிறது (naai kuraikkirathu) - The dog is barking",
        "பறவை பறக்கிறது (paravai parakkiRathu) - The bird is flying", 
        "மீன் நீந்துகிறது (meen neenthukiRathu) - The fish is swimming",
        "பெரிய யானை (periya yaanai) - big elephant",
        "சிறிய எலி (siriya eli) - small mouse",
        "அழகான பூ (azhagaana poo) - beautiful flower",
        "நாய்கள் (naaikal) - dogs",
        "பூனைகள் (poonaikal) - cats",
        "பறவைகள் (paravaikal) - birds"
    ]
}

def generate_audio(text: str, output_file: str) -> bool:
    """Generate audio using ElevenLabs API"""
    try:
        print(f"🎵 Generating: {text}")
        
        url = f"https://api.elevenlabs.io/v1/text-to-speech/{VOICE_ID}"
        
        headers = {
            "Accept": "audio/mpeg",
            "Content-Type": "application/json",
            "xi-api-key": ELEVENLABS_API_KEY
        }
        
        data = {
            "text": text,
            "model_id": "eleven_multilingual_v2",
            "voice_settings": {
                "stability": 0.5,
                "similarity_boost": 0.8,
                "style": 0.2,
                "use_speaker_boost": True
            }
        }
        
        response = requests.post(url, json=data, headers=headers)
        
        if response.status_code == 200:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            # Save audio file
            with open(output_file, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ Saved: {output_file}")
            return True
        else:
            print(f"❌ Error {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Generate all audio files for Animals and Nature lesson"""
    
    if ELEVENLABS_API_KEY == "YOUR_REAL_ELEVENLABS_API_KEY_HERE":
        print("❌ Please update ELEVENLABS_API_KEY with your real API key!")
        return
    
    print("🚀 Starting audio generation for Animals and Nature lesson")
    
    output_dir = "Assets/Audio/Tamil/A1/Lesson24"
    generated_count = 0
    
    # 1. Generate vocabulary audio
    print("\n📚 Generating vocabulary audio...")
    for i, vocab in enumerate(LESSON_CONTENT["vocabulary"], 1):
        # Word audio
        word_file = f"{output_dir}/vocab_{i:02d}_word.mp3"
        if generate_audio(vocab["word"], word_file):
            generated_count += 1
        
        # Example audio  
        example_file = f"{output_dir}/vocab_{i:02d}_example.mp3"
        if generate_audio(vocab["example"], example_file):
            generated_count += 1
        
        time.sleep(1)  # Rate limiting
    
    # 2. Generate conversation audio
    print("\n💬 Generating conversation audio...")
    for i, conv in enumerate(LESSON_CONTENT["conversations"], 1):
        conv_file = f"{output_dir}/conv_{i:02d}.mp3"
        if generate_audio(conv["text"], conv_file):
            generated_count += 1
        
        time.sleep(1)  # Rate limiting
    
    # 3. Generate grammar examples audio
    print("\n📖 Generating grammar examples audio...")
    for i, example in enumerate(LESSON_CONTENT["grammar_examples"], 1):
        grammar_file = f"{output_dir}/grammar_{i:02d}.mp3"
        if generate_audio(example, grammar_file):
            generated_count += 1
        
        time.sleep(1)  # Rate limiting
    
    print(f"\n🎉 Audio generation complete!")
    print(f"📊 Generated {generated_count} audio files")
    print(f"📁 Files saved to: {output_dir}")
    print("\n📋 Next steps:")
    print("1. Upload audio files to Supabase Storage")
    print("2. Update lesson content_metadata with audio URLs")
    print("3. Test audio playback in the app")

if __name__ == "__main__":
    main()
