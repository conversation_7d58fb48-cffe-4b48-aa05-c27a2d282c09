import SwiftUI

struct ExerciseDetailView: View {
    let exercises: [LessonExerciseItem]
    @Environment(\.dismiss) private var dismiss
    @State private var currentIndex = 0
    @State private var selectedAnswers: [Int?] = []
    @State private var showResults: [Bool] = []
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Main content
                if !exercises.isEmpty {
                    TabView(selection: $currentIndex) {
                        ForEach(Array(exercises.enumerated()), id: \.offset) { index, exercise in
                            ExerciseDetailCard(
                                exercise: exercise,
                                exerciseIndex: index,
                                selectedAnswer: binding(for: index),
                                showResult: showResultBinding(for: index)
                            )
                            .tag(index)
                        }
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .automatic))
                    .indexViewStyle(PageIndexViewStyle(backgroundDisplayMode: .always))
                } else {
                    emptyStateView
                }
                
                // Bottom controls
                bottomControls
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            selectedAnswers = Array(repeating: nil, count: exercises.count)
            showResults = Array(repeating: false, count: exercises.count)
        }
    }
    
    private func binding(for index: Int) -> Binding<Int?> {
        return Binding<Int?>(
            get: {
                guard index < selectedAnswers.count else { return nil }
                return selectedAnswers[index]
            },
            set: { newValue in
                guard index < selectedAnswers.count else { return }
                selectedAnswers[index] = newValue
            }
        )
    }

    private func showResultBinding(for index: Int) -> Binding<Bool> {
        return Binding<Bool>(
            get: {
                guard index < showResults.count else { return false }
                return showResults[index]
            },
            set: { newValue in
                guard index < showResults.count else { return }
                showResults[index] = newValue
            }
        )
    }
    
    private var headerView: some View {
        VStack(spacing: 0) {
            HStack {
                Button(action: { dismiss() }) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                        Text("Back")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(.primary)
                }
                
                Spacer()
                
                Text("Practice")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button(action: {
                    // Reset only the current exercise
                    print("🔄 Resetting exercise \(currentIndex + 1)")
                    if currentIndex < selectedAnswers.count {
                        selectedAnswers[currentIndex] = nil
                        print("✅ Cleared selected answer for exercise \(currentIndex + 1)")
                    }
                    if currentIndex < showResults.count {
                        showResults[currentIndex] = false
                        print("✅ Cleared result display for exercise \(currentIndex + 1)")
                    }
                }) {
                    Text("Reset")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.orange)
                }
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            .padding(.bottom, 20)
            
            if !exercises.isEmpty {
                Text("\(currentIndex + 1) of \(exercises.count)")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.secondary)
                    .padding(.bottom, 20)
            }
        }
        .background(Color(.systemBackground))
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "pencil.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.orange.opacity(0.3))
            
            Text("No Exercises")
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.primary)
            
            Text("Practice exercises will be available soon")
                .font(.system(size: 16))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
    
    private var bottomControls: some View {
        VStack(spacing: 16) {
            if !exercises.isEmpty {
                HStack(spacing: 20) {
                    Button(action: {
                        withAnimation {
                            currentIndex = max(0, currentIndex - 1)
                        }
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "chevron.left")
                            Text("Previous")
                        }
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(currentIndex > 0 ? .orange : .gray)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                    }
                    .disabled(currentIndex <= 0)
                    
                    Button(action: {
                        withAnimation {
                            currentIndex = min(exercises.count - 1, currentIndex + 1)
                        }
                    }) {
                        HStack(spacing: 8) {
                            Text("Next")
                            Image(systemName: "chevron.right")
                        }
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(currentIndex < exercises.count - 1 ? .orange : .gray)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                    }
                    .disabled(currentIndex >= exercises.count - 1)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
        .background(Color(.systemBackground))
    }
}

struct ExerciseDetailCard: View {
    let exercise: LessonExerciseItem
    let exerciseIndex: Int
    @Binding var selectedAnswer: Int?
    @Binding var showResult: Bool

    private func romanize(_ text: String) -> String {
        let tamilToRoman: [String: String] = [
            "நாய்": "naai", "பூனை": "poonai", "யானை": "yaanai", "சிங்கம்": "singam",
            "மீன்": "meen", "பறவை": "paravai", "பாம்பு": "paambu", "எலி": "eli",
            "பசு": "pasu", "முயல்": "muyal", "தவளை": "thavalai", "தேனீ": "thenee",
            "வண்ணத்துப்பூச்சி": "vannatthuppoochi", "எறும்பு": "erumbu",
            "மரம்": "maram", "பூ": "poo", "இலை": "ilai", "புல்": "pul",
            "சூரியன்": "sooriyaan", "நிலா": "nila", "நட்சத்திரம்": "natchathiram",
            "பறக்கிறது": "parakkiRathu", "நடக்கிறது": "nadakkiRathu",
            "ஓடுகிறது": "odugiRathu", "நீந்துகிறது": "neenthukiRathu",
            "குரைக்கிறது": "kuraikkirathu", "தூங்குகிறது": "thoongukiRathu",
            "பிரகாசிக்கிறது": "piragaasikkirathu", "மறைகிறது": "maRaikkirathu",
            "வேலை செய்கிறது": "velai seykkirathu", "சேகரிக்கிறது": "segarikkirathu",
            "ஊர்ந்து": "oornthu", "கர்ஜிக்கிறது": "karjikkirathu",
            "மியாவ்": "miyaav", "பாடுகிறது": "paadukiRathu"
        ]

        return tamilToRoman[text] ?? ""
    }

    private func backgroundColor(for index: Int) -> Color {
        if selectedAnswer == index {
            return Color.orange.opacity(0.1)
        } else if showResult && index == exercise.correctAnswer {
            return Color.green.opacity(0.1)
        } else if showResult && selectedAnswer == index && index != exercise.correctAnswer {
            return Color.red.opacity(0.1)
        } else {
            return Color(.systemGray6).opacity(0.5)
        }
    }

    private func borderColor(for index: Int) -> Color {
        if selectedAnswer == index {
            return Color.orange
        } else if showResult && index == exercise.correctAnswer {
            return Color.green
        } else if showResult && selectedAnswer == index && index != exercise.correctAnswer {
            return Color.red
        } else {
            return Color.clear
        }
    }

    private func shouldShowBorder(for index: Int) -> Bool {
        return selectedAnswer == index ||
               (showResult && index == exercise.correctAnswer) ||
               (showResult && selectedAnswer == index && index != exercise.correctAnswer)
    }

    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Exercise header
                VStack(spacing: 12) {
                    HStack {
                        Text("Exercise \(exerciseIndex + 1)")
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(Color.orange)
                            .cornerRadius(8)
                        
                        Spacer()
                        
                        Text("\(exercise.points) pts")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.orange)
                    }
                    
                    Text(exercise.type.capitalized.replacingOccurrences(of: "_", with: " "))
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                        .textCase(.uppercase)
                }
                
                // Question
                Text(exercise.question)
                    .font(.system(size: 22, weight: .semibold))
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
                
                // Options
                if !exercise.options.isEmpty {
                    VStack(spacing: 12) {
                        ForEach(Array(exercise.options.enumerated()), id: \.offset) { index, option in
                            Button(action: {
                                selectedAnswer = index
                                // Automatically show result when answer is selected
                                showResult = true
                            }) {
                                HStack {
                                    Text("\(index + 1).")
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(.orange)
                                        .frame(width: 30, alignment: .leading)
                                    
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text(option)
                                            .font(.system(size: 16))
                                            .foregroundColor(.primary)
                                            .multilineTextAlignment(.leading)

                                        // Note: Romanization is shown in the explanation instead of for each option
                                        // This provides better UX and matches our database structure
                                    }
                                    
                                    Spacer()
                                    
                                    if selectedAnswer == index {
                                        Image(systemName: "checkmark.circle.fill")
                                            .font(.system(size: 16))
                                            .foregroundColor(.orange)
                                    } else if showResult && index == exercise.correctAnswer {
                                        Image(systemName: "checkmark.circle.fill")
                                            .font(.system(size: 16))
                                            .foregroundColor(.green)
                                    } else if showResult && selectedAnswer == index && index != exercise.correctAnswer {
                                        Image(systemName: "xmark.circle.fill")
                                            .font(.system(size: 16))
                                            .foregroundColor(.red)
                                    }
                                }
                                .padding(16)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(backgroundColor(for: index))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 12)
                                                .stroke(
                                                    borderColor(for: index),
                                                    lineWidth: shouldShowBorder(for: index) ? 2 : 0
                                                )
                                        )
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                }

                // Feedback message (shown when answer is selected)
                if showResult, let selectedAnswer = selectedAnswer {
                    VStack(spacing: 12) {
                        HStack {
                            if selectedAnswer == exercise.correctAnswer {
                                Image(systemName: "checkmark.circle.fill")
                                    .font(.title2)
                                    .foregroundColor(.green)
                                Text("Correct!")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.green)
                            } else {
                                Image(systemName: "xmark.circle.fill")
                                    .font(.title2)
                                    .foregroundColor(.red)
                                Text("Incorrect")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.red)
                            }
                            Spacer()
                        }

                        if selectedAnswer != exercise.correctAnswer {
                            HStack {
                                Text("Correct answer:")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                Text(exercise.options[exercise.correctAnswer])
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.green)
                                Spacer()
                            }
                        }
                    }
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(selectedAnswer == exercise.correctAnswer ? Color.green.opacity(0.1) : Color.red.opacity(0.1))
                    )
                }

                // Explanation (shown when results are visible)
                if showResult, let explanation = exercise.explanation {
                    VStack(spacing: 12) {
                        HStack {
                            Text("💡 Explanation")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.secondary)
                                .textCase(.uppercase)
                            Spacer()
                        }
                        
                        Text(explanation)
                            .font(.system(size: 16))
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.leading)
                    }
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.blue.opacity(0.05))
                    )
                }
                
                Spacer()
            }
            .padding(20)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#Preview {
    let sampleExercises = [
        LessonExerciseItem(
            type: "multiple_choice",
            question: "What is the Tamil word for 'dog'?",
            options: ["நாய்", "பூனை", "யானை", "சிங்கம்"],
            correctAnswer: 0,
            explanation: "நாய் means dog in Tamil",
            points: 10
        ),
        LessonExerciseItem(
            type: "fill_in_blank",
            question: "Complete: பறவை ______ (flies)",
            options: ["பறக்கிறது", "நடக்கிறது", "ஓடுகிறது", "நீந்துகிறது"],
            correctAnswer: 0,
            explanation: "Birds fly, so பறக்கிறது is correct",
            points: 15
        )
    ]
    
    ExerciseDetailView(exercises: sampleExercises)
}
