import SwiftUI

struct GrammarDetailView: View {
    let grammarPoints: [LessonGrammarPoint]
    @Environment(\.dismiss) private var dismiss
    @State private var currentIndex = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Main content
                if !grammarPoints.isEmpty {
                    TabView(selection: $currentIndex) {
                        ForEach(Array(grammarPoints.enumerated()), id: \.offset) { index, grammar in
                            GrammarDetailCard(grammarPoint: grammar)
                                .tag(index)
                        }
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .automatic))
                    .indexViewStyle(PageIndexViewStyle(backgroundDisplayMode: .always))
                } else {
                    emptyStateView
                }
                
                // Bottom controls
                bottomControls
            }
        }
        .navigationBarHidden(true)
    }
    
    private var headerView: some View {
        VStack(spacing: 0) {
            HStack {
                Button(action: { dismiss() }) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                        Text("Back")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(.primary)
                }
                
                Spacer()
                
                Text("Grammar")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)
                
                Spacer()
                
                // Placeholder for symmetry
                HStack(spacing: 8) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .medium))
                    Text("Back")
                        .font(.system(size: 16, weight: .medium))
                }
                .opacity(0)
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            .padding(.bottom, 20)
            
            if !grammarPoints.isEmpty {
                Text("\(currentIndex + 1) of \(grammarPoints.count)")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.secondary)
                    .padding(.bottom, 20)
            }
        }
        .background(Color(.systemBackground))
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "textformat.abc")
                .font(.system(size: 60))
                .foregroundColor(.blue.opacity(0.3))
            
            Text("No Grammar Points")
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.primary)
            
            Text("Grammar content will be available soon")
                .font(.system(size: 16))
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
    
    private var bottomControls: some View {
        VStack(spacing: 16) {
            if !grammarPoints.isEmpty {
                HStack(spacing: 20) {
                    Button(action: {
                        withAnimation {
                            currentIndex = max(0, currentIndex - 1)
                        }
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "chevron.left")
                            Text("Previous")
                        }
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(currentIndex > 0 ? .blue : .gray)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                    }
                    .disabled(currentIndex <= 0)
                    
                    Button(action: {
                        withAnimation {
                            currentIndex = min(grammarPoints.count - 1, currentIndex + 1)
                        }
                    }) {
                        HStack(spacing: 8) {
                            Text("Next")
                            Image(systemName: "chevron.right")
                        }
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(currentIndex < grammarPoints.count - 1 ? .blue : .gray)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                    }
                    .disabled(currentIndex >= grammarPoints.count - 1)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
        .background(Color(.systemBackground))
    }
}

struct GrammarDetailCard: View {
    let grammarPoint: LessonGrammarPoint

    // Helper function to generate romanization for Tamil text
    private func romanize(_ tamilText: String) -> String {
        let tamilToRoman: [String: String] = [
            "நாய்": "naai",
            "பூனை": "poonai",
            "யானை": "yaanai",
            "சிங்கம்": "singam",
            "பறவை": "paravai",
            "மீன்": "meen",
            "குரங்கு": "kurangu",
            "மான்": "maan",
            "கரடி": "karadi",
            "பாம்பு": "paambu",
            "தவளை": "thavalai",
            "பட்டாம்பூச்சி": "pattaampuchi",
            "எறும்பு": "erumbu",
            "தேனீ": "thenee",
            "பறக்கிறது": "parakkiRathu",
            "நடக்கிறது": "nadakkiRathu",
            "ஓடுகிறது": "odugiRathu",
            "நீந்துகிறது": "neenthukiRathu",
            "குரைக்கிறது": "kuraikkirathu",
            "மியாவ்": "miyaav",
            "கர்ஜிக்கிறது": "karjikkirathu",
            "கிறது": "kiRathu",
            "கிறார்": "kiRaar",
            "கிறேன்": "kiRen"
        ]

        // For longer sentences, try to match individual words
        let words = tamilText.components(separatedBy: " ")
        let romanizedWords = words.map { word in
            // Remove punctuation for matching
            let cleanWord = word.trimmingCharacters(in: CharacterSet.punctuationCharacters)
            return tamilToRoman[cleanWord] ?? cleanWord
        }

        return romanizedWords.joined(separator: " ")
    }

    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Grammar rule title
                VStack(spacing: 12) {
                    Text(grammarPoint.rule)
                        .font(.system(size: 28, weight: .bold))
                        .foregroundColor(.blue)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 20)
                }
                
                // Explanation
                VStack(spacing: 12) {
                    HStack {
                        Text("Explanation")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.secondary)
                            .textCase(.uppercase)
                        Spacer()
                    }
                    
                    Text(grammarPoint.explanation)
                        .font(.system(size: 18))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)
                }
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6).opacity(0.5))
                )
                
                // Examples
                if !grammarPoint.examples.isEmpty {
                    VStack(spacing: 12) {
                        HStack {
                            Text("Examples")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.secondary)
                                .textCase(.uppercase)
                            Spacer()
                        }
                        
                        VStack(spacing: 12) {
                            ForEach(Array(grammarPoint.examples.enumerated()), id: \.offset) { index, example in
                                VStack(spacing: 4) {
                                    HStack {
                                        Text("\(index + 1).")
                                            .font(.system(size: 16, weight: .medium))
                                            .foregroundColor(.blue)
                                            .frame(width: 25, alignment: .leading)

                                        VStack(alignment: .leading, spacing: 4) {
                                            Text(example)
                                                .font(.system(size: 16))
                                                .foregroundColor(.primary)

                                            // Add romanization for Tamil text
                                            if example.contains("நாய்") || example.contains("பூனை") || example.contains("யானை") || example.contains("கிறது") {
                                                Text("[\(romanize(example))]")
                                                    .font(.system(size: 14, weight: .medium))
                                                    .foregroundColor(.green)
                                                    .italic()
                                            }
                                        }

                                        Spacer()

                                        // Audio button for examples
                                        EnhancedAudioButton(
                                            text: example,
                                            audioURL: nil,
                                            context: "grammar_example",
                                            size: 32,
                                            color: .blue
                                        )
                                    }
                                }
                                .padding(.vertical, 4)
                            }
                        }
                    }
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.blue.opacity(0.05))
                    )
                }
                
                // Tips
                if let tips = grammarPoint.tips, !tips.isEmpty {
                    VStack(spacing: 12) {
                        HStack {
                            Text("💡 Tips")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.secondary)
                                .textCase(.uppercase)
                            Spacer()
                        }
                        
                        Text(tips)
                            .font(.system(size: 16))
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.leading)
                    }
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.orange.opacity(0.05))
                    )
                }
                
                Spacer()
            }
            .padding(20)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#Preview {
    let sampleGrammar = [
        LessonGrammarPoint(
            rule: "Present Continuous Tense",
            explanation: "Use கிறது/கிறார்/கிறேன் to show ongoing actions in Tamil",
            examples: ["நாய் குரைக்கிறது (The dog is barking)", "பறவை பறக்கிறது (The bird is flying)", "மீன் நீந்துகிறது (The fish is swimming)"],
            tips: "Practice with animal actions to remember the pattern"
        ),
        LessonGrammarPoint(
            rule: "Animal Names",
            explanation: "Tamil animal names are often descriptive of the animal's characteristics",
            examples: ["நாய் (dog)", "பூனை (cat)", "யானை (elephant)"],
            tips: "Learn animals by their sounds and characteristics"
        )
    ]
    
    GrammarDetailView(grammarPoints: sampleGrammar)
}
