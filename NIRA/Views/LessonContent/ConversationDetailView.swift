import SwiftUI

struct ConversationDetailView: View {
    let conversations: [LessonDialogueItem]
    @Environment(\.dismiss) private var dismiss
    @State private var currentIndex = 0
    @State private var autoPlay = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Main content
                if !conversations.isEmpty {
                    TabView(selection: $currentIndex) {
                        ForEach(Array(conversations.enumerated()), id: \.offset) { index, conversation in
                            ConversationCard(
                                conversation: conversation,
                                autoPlay: $autoPlay
                            )
                            .tag(index)
                        }
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .automatic))
                    .indexViewStyle(PageIndexViewStyle(backgroundDisplayMode: .always))
                } else {
                    emptyStateView
                }
                
                // Bottom controls
                bottomControls
            }
        }
        .navigationBarHidden(true)
    }
    
    private var headerView: some View {
        VStack(spacing: 16) {
            HStack {
                Button(action: { dismiss() }) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                        Text("Back")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(.primary)
                }
                
                Spacer()
                
                Text("Conversations")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.primary)
                
                Spacer()
                
                // Placeholder for symmetry
                HStack(spacing: 8) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .medium))
                    Text("Back")
                        .font(.system(size: 16, weight: .medium))
                }
                .opacity(0)
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            
            if !conversations.isEmpty {
                Text("\(currentIndex + 1) of \(conversations.count)")
                    .font(.system(size: 16))
                    .foregroundColor(.secondary)
            }
        }
        .padding(.bottom, 20)
        .background(Color(.systemBackground))
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "message.circle")
                .font(.system(size: 48))
                .foregroundColor(.gray)
            
            Text("No Conversations")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text("This lesson doesn't have conversation examples yet.")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var bottomControls: some View {
        VStack(spacing: 16) {
            // Auto-play toggle
            Button(action: {
                autoPlay.toggle()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: autoPlay ? "speaker.slash" : "speaker.wave.2")
                        .font(.system(size: 16, weight: .medium))
                    Text(autoPlay ? "Manual Audio" : "Auto Audio")
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.purple)
                .padding(.horizontal, 16)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill(Color.purple.opacity(0.1))
                )
            }
            
            // Navigation buttons
            if conversations.count > 1 {
                HStack(spacing: 20) {
                    Button(action: {
                        withAnimation {
                            currentIndex = max(0, currentIndex - 1)
                        }
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "chevron.left")
                            Text("Previous")
                        }
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(currentIndex > 0 ? .blue : .gray)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                    }
                    .disabled(currentIndex <= 0)
                    
                    Button(action: {
                        withAnimation {
                            currentIndex = min(conversations.count - 1, currentIndex + 1)
                        }
                    }) {
                        HStack(spacing: 8) {
                            Text("Next")
                            Image(systemName: "chevron.right")
                        }
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(currentIndex < conversations.count - 1 ? .blue : .gray)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                    }
                    .disabled(currentIndex >= conversations.count - 1)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
        .background(Color(.systemBackground))
    }
}

struct ConversationCard: View {
    let conversation: LessonDialogueItem
    @Binding var autoPlay: Bool

    // Helper function to generate romanization for Tamil text
    private func romanize(_ tamilText: String) -> String {
        // Basic Tamil to Roman transliteration mapping
        let tamilToRoman: [String: String] = [
            // Basic phrases and questions
            "வணக்கம்": "vanakkam",
            "இது": "idhu",
            "என்ன": "enna",
            "விலங்கு": "vilangku",
            "இது என்ன விலங்கு?": "idhu enna vilangku?",
            "இந்த": "indha",
            "அழகாக": "azhagaaga",
            "இருக்கிறது": "irukkiRathu",
            "இந்த பூ அழகாக இருக்கிறது": "indha poo azhagaaga irukkiRathu",

            // Animals
            "நாய்": "naai",
            "பூனை": "poonai",
            "யானை": "yaanai",
            "சிங்கம்": "singam",
            "பறவை": "paravai",
            "மீன்": "meen",
            "குரங்கு": "kurangu",
            "மான்": "maan",
            "கரடி": "karadi",
            "பாம்பு": "paambu",
            "தவளை": "thavalai",
            "பட்டாம்பூச்சி": "pattaampuchi",
            "எறும்பு": "erumbu",
            "தேனீ": "thenee",
            "எலி": "eli",
            "பசு": "pasu",
            "முயல்": "muyal",

            // Nature
            "மரம்": "maram",
            "பூ": "poo",
            "இலை": "ilai",
            "புல்": "pul",
            "சூரியன்": "sooriyaan",
            "நிலா": "nila",
            "நட்சத்திரம்": "natchathiram",

            // Actions
            "பறக்கிறது": "parakkiRathu",
            "நடக்கிறது": "nadakkiRathu",
            "ஓடுகிறது": "odugiRathu",
            "நீந்துகிறது": "neenthukiRathu",
            "குரைக்கிறது": "kuraikkirathu",
            "மியாவ்": "miyaav",
            "கர்ஜிக்கிறது": "karjikkirathu",
            "தூங்குகிறது": "thoongukiRathu",
            "பிரகாசிக்கிறது": "piragaasikkirathu"
        ]

        // Try to find exact match first
        if let roman = tamilToRoman[tamilText] {
            return roman
        }

        // For longer sentences, try to match individual words
        let words = tamilText.components(separatedBy: " ")
        let romanizedWords = words.map { word in
            // Remove punctuation for matching
            let cleanWord = word.trimmingCharacters(in: CharacterSet.punctuationCharacters)
            if let romanized = tamilToRoman[cleanWord] {
                return romanized + (word.hasSuffix("?") ? "?" : "")
            }
            return tamilToRoman[word] ?? word
        }

        return romanizedWords.joined(separator: " ")
    }
    
    var body: some View {
        VStack(spacing: 24) {
            // Speaker and text
            VStack(spacing: 16) {
                // Speaker label
                HStack {
                    Text(conversation.speaker)
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(.purple)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.purple.opacity(0.1))
                        )
                    
                    Spacer()
                    
                    // Enhanced audio button with fallback generation
                    EnhancedAudioButton(
                        text: conversation.text,
                        audioURL: conversation.audioURL,
                        context: "conversation",
                        size: 44,
                        color: .purple
                    )
                }
                
                // Main text with romanization
                VStack(spacing: 8) {
                    Text(conversation.text)
                        .font(.system(size: 28, weight: .semibold))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 20)

                    // Romanization
                    Text("[\(romanize(conversation.text))]")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.green)
                        .italic()
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 20)
                }
            }

            // Translation (always shown)
            if let translation = conversation.translation {
                VStack(spacing: 8) {
                    Text("Translation")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.secondary)
                        .textCase(.uppercase)

                    Text(translation)
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.blue)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 20)
                }
            }
            
            // Cultural note (if available)
            if let culturalNote = conversation.culturalNote {
                VStack(spacing: 8) {
                    HStack {
                        Image(systemName: "lightbulb.fill")
                            .font(.system(size: 16))
                            .foregroundColor(.orange)
                        
                        Text("Cultural Note")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.orange)
                            .textCase(.uppercase)
                        
                        Spacer()
                    }
                    
                    Text(culturalNote)
                        .font(.system(size: 16))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.leading)
                }
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.orange.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                        )
                )
            }
            
            Spacer()
        }
        .padding(20)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    

}

#Preview {
    ConversationDetailView(conversations: [
        LessonDialogueItem(
            speaker: "Person A",
            text: "வணக்கம்!",
            translation: "Hello!",
            culturalNote: "This is the most common greeting in Tamil, used at any time of day.",
            audioURL: nil
        ),
        LessonDialogueItem(
            speaker: "Person B",
            text: "வணக்கம்! எப்படி இருக்கிறீர்கள்?",
            translation: "Hello! How are you?",
            culturalNote: nil,
            audioURL: nil
        )
    ])
}
