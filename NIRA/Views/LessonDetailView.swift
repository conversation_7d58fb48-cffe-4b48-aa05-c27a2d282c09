import SwiftUI
import Foundation

// MARK: - Lesson Detail View with Modern Design

struct LessonDetailView: View {
    let lesson: SupabaseLesson
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) var colorScheme
    @State private var animateElements = false
    @StateObject private var userPreferences = UserPreferencesService.shared

    // Extracted content from JSON
    @State private var extractedVocabulary: [LessonVocabularyItem] = []
    @State private var extractedDialogues: [LessonDialogueItem] = []
    @State private var extractedExercises: [LessonExerciseItem] = []
    @State private var extractedGrammarPoints: [LessonGrammarPoint] = []

    // Navigation states
    @State private var showingVocabularyDetail = false
    @State private var showingConversationDetail = false
    @State private var showingGrammarDetail = false
    @State private var showingExerciseDetail = false

    // Level-based colors for this lesson
    private var levelColor: Color {
        return Color.getLevelColor(for: lesson.difficultyLevel ?? 1)
    }

    private var levelGradient: [Color] {
        return Color.getLevelGradient(for: lesson.difficultyLevel ?? 1)
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background that respects system theme
                (colorScheme == .dark ? Color.black : Color(.systemBackground))
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Header with back button and title
                    lessonHeader

                    // Main content with tile-based categories
                    ScrollView {
                        LazyVStack(spacing: 24) {
                            // Vocabulary Category
                            CategoryTileSection(
                                title: "Vocabulary Practice",
                                subtitle: "Learn essential words and phrases",
                                icon: "book.fill",
                                color: .emojiGreen,
                                items: extractedVocabulary.isEmpty ? getFallbackVocabulary() : extractedVocabulary.map { vocab in
                                    TileItem(
                                        title: vocab.word,
                                        subtitle: vocab.translation ?? "",
                                        color: .emojiGreen,
                                        onTap: {
                                            showingVocabularyDetail = true
                                        }
                                    )
                                }
                            )

                            // Dialogues Category
                            CategoryTileSection(
                                title: "Guided Conversations",
                                subtitle: "Practice real-world dialogues",
                                icon: "message.fill",
                                color: .emojiPurple,
                                items: extractedDialogues.isEmpty ? getFallbackDialogues() : extractedDialogues.map { dialogue in
                                    TileItem(
                                        title: dialogue.text,
                                        subtitle: dialogue.translation ?? "",
                                        color: .emojiPurple,
                                        onTap: {
                                            showingConversationDetail = true
                                        }
                                    )
                                }
                            )

                            // Grammar Category
                            CategoryTileSection(
                                title: "Grammar Essentials",
                                subtitle: "Master language structure",
                                icon: "textformat.abc",
                                color: .emojiBlue,
                                items: extractedGrammarPoints.isEmpty ? getFallbackGrammar() : extractedGrammarPoints.map { grammar in
                                    TileItem(
                                        title: grammar.rule,
                                        subtitle: grammar.explanation,
                                        color: .emojiBlue
                                    )
                                }
                            )

                            // Exercises Category
                            CategoryTileSection(
                                title: "Practice Exercises",
                                subtitle: "Test your knowledge",
                                icon: "pencil.circle.fill",
                                color: .emojiOrange,
                                items: extractedExercises.isEmpty ? getFallbackExercises() : extractedExercises.map { exercise in
                                    TileItem(
                                        title: exercise.question,
                                        subtitle: exercise.type.capitalized,
                                        color: .emojiOrange
                                    )
                                }
                            )
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 16)
                    }
                }
            }
        }
        .navigationBarHidden(true)
        .onAppear {
            withAnimation(.easeOut(duration: 1.0)) {
                animateElements = true
            }
            extractContentFromMetadata()
        }
        .sheet(isPresented: $showingVocabularyDetail) {
            VocabularyDetailView(vocabularyItems: extractedVocabulary.isEmpty ? getFallbackVocabularyItems() : extractedVocabulary)
        }
        .sheet(isPresented: $showingConversationDetail) {
            ConversationDetailView(conversations: extractedDialogues)
        }
    }

    // MARK: - Header

    private var lessonHeader: some View {
        VStack(spacing: 0) {
            // Simple navigation header
            HStack {
                Button(action: { dismiss() }) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .medium))
                        Text("Back")
                            .font(.system(size: 16, weight: .medium))
                    }
                    .foregroundColor(.primary)
                }

                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.top, 10)
            .padding(.bottom, 20)

            // Lesson title
            HStack {
                Text(lesson.title)
                    .font(.system(size: 28, weight: .bold))
                    .foregroundColor(.primary)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)

                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 30)
        }
    }





    // MARK: - Content Extraction

    private func extractContentFromMetadata() {
        print("🔍 Checking lesson content metadata...")
        print("🔍 Lesson title: \(lesson.title)")
        print("🔍 Content metadata exists: \(lesson.contentMetadata != nil)")

        guard let contentMetadata = lesson.contentMetadata?.value as? [String: Any] else {
            print("❌ No content metadata found or wrong type")
            print("🔍 Raw contentMetadata: \(String(describing: lesson.contentMetadata))")
            return
        }

        print("✅ Content metadata found with keys: \(contentMetadata.keys.sorted())")

        // Extract vocabulary
        if let vocabularyArray = contentMetadata["vocabulary"] as? [[String: Any]] {
            extractedVocabulary = vocabularyArray.compactMap { vocabDict in
                guard let word = vocabDict["word"] as? String else { return nil }

                // Get example from the first dialogue if available
                var example: String? = nil
                if let dialogues = vocabDict["dialogues"] as? [[String: Any]],
                   let firstDialogue = dialogues.first,
                   let dialogueText = firstDialogue["text"] as? String {
                    example = dialogueText
                }

                return LessonVocabularyItem(
                    word: word,
                    translation: vocabDict["translation"] as? String,
                    pronunciation: vocabDict["pronunciation"] as? String,
                    partOfSpeech: vocabDict["part_of_speech"] as? String,
                    example: example,
                    wordAudioURL: vocabDict["word_audio_url"] as? String,
                    exampleAudioURL: vocabDict["example_audio_url"] as? String
                )
            }
        }

        print("✅ Extracted \(extractedVocabulary.count) vocabulary items from metadata")

        // Extract dialogues from conversations
        if let conversationsArray = contentMetadata["conversations"] as? [[String: Any]] {
            extractedDialogues = []
            for conversation in conversationsArray {
                if let audioExchanges = conversation["audio_exchanges"] as? [[String: Any]] {
                    for exchange in audioExchanges {
                        if let text = exchange["text"] as? String,
                           let speaker = exchange["speaker"] as? String {
                            let dialogueItem = LessonDialogueItem(
                                speaker: speaker,
                                text: text,
                                translation: nil, // Translation is in the main exchanges, not audio_exchanges
                                culturalNote: nil,
                                audioURL: exchange["audio_url"] as? String
                            )
                            extractedDialogues.append(dialogueItem)
                        }
                    }
                }
            }
        }

        // Extract exercises
        if let exercisesArray = contentMetadata["exercises"] as? [[String: Any]] {
            extractedExercises = exercisesArray.compactMap { exerciseDict in
                guard let question = exerciseDict["question"] as? String,
                      let type = exerciseDict["type"] as? String else { return nil }
                return LessonExerciseItem(
                    type: type,
                    question: question,
                    options: exerciseDict["options"] as? [String] ?? [],
                    correctAnswer: exerciseDict["correct_answer"] as? Int ?? 0,
                    explanation: exerciseDict["explanation"] as? String,
                    points: exerciseDict["points"] as? Int ?? 10
                )
            }
        }

        // Extract grammar points
        if let grammarArray = contentMetadata["grammar_points"] as? [[String: Any]] {
            extractedGrammarPoints = grammarArray.compactMap { grammarDict in
                guard let rule = grammarDict["rule"] as? String else { return nil }
                return LessonGrammarPoint(
                    rule: rule,
                    explanation: grammarDict["explanation"] as? String ?? "",
                    examples: grammarDict["examples"] as? [String] ?? [],
                    tips: grammarDict["tips"] as? String
                )
            }
        }

        print("✅ Extracted content: \(extractedVocabulary.count) vocab, \(extractedDialogues.count) dialogues, \(extractedExercises.count) exercises")
    }

    // MARK: - Fallback Content

    private func getFallbackVocabulary() -> [TileItem] {
        return [
            TileItem(title: "Hello", subtitle: "Basic greeting", color: .emojiGreen, onTap: { showingVocabularyDetail = true }),
            TileItem(title: "Thank you", subtitle: "Expression of gratitude", color: .emojiGreen, onTap: { showingVocabularyDetail = true }),
            TileItem(title: "Please", subtitle: "Polite request", color: .emojiGreen, onTap: { showingVocabularyDetail = true }),
            TileItem(title: "Excuse me", subtitle: "Getting attention", color: .emojiGreen, onTap: { showingVocabularyDetail = true }),
            TileItem(title: "Good morning", subtitle: "Morning greeting", color: .emojiGreen, onTap: { showingVocabularyDetail = true }),
            TileItem(title: "Good evening", subtitle: "Evening greeting", color: .emojiGreen, onTap: { showingVocabularyDetail = true }),
            TileItem(title: "How are you?", subtitle: "Common question", color: .emojiGreen, onTap: { showingVocabularyDetail = true }),
            TileItem(title: "I'm fine", subtitle: "Standard response", color: .emojiGreen, onTap: { showingVocabularyDetail = true }),
            TileItem(title: "What's your name?", subtitle: "Introduction question", color: .emojiGreen, onTap: { showingVocabularyDetail = true }),
            TileItem(title: "My name is...", subtitle: "Name introduction", color: .emojiGreen, onTap: { showingVocabularyDetail = true }),
            TileItem(title: "Nice to meet you", subtitle: "First meeting", color: .emojiGreen, onTap: { showingVocabularyDetail = true }),
            TileItem(title: "See you later", subtitle: "Farewell phrase", color: .emojiGreen, onTap: { showingVocabularyDetail = true })
        ]
    }

    private func getFallbackVocabularyItems() -> [LessonVocabularyItem] {
        return [
            LessonVocabularyItem(
                word: "வணக்கம்",
                translation: "Hello/Greetings",
                pronunciation: "/vaṇakkam/",
                partOfSpeech: "noun",
                example: "வணக்கம், எப்படி இருக்கிறீர்கள்?",
                wordAudioURL: "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_1_vocab_1_word.mp3",
                exampleAudioURL: "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_1_vocab_1_example.mp3"
            ),
            LessonVocabularyItem(
                word: "நன்றி",
                translation: "Thank you",
                pronunciation: "/nanṟi/",
                partOfSpeech: "noun",
                example: "உதவிக்கு நன்றி.",
                wordAudioURL: "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_1_vocab_4_word.mp3",
                exampleAudioURL: "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_1_vocab_4_example.mp3"
            ),
            LessonVocabularyItem(
                word: "தயவுசெய்து",
                translation: "Please",
                pronunciation: "/tayavuceitu/",
                partOfSpeech: "adverb",
                example: "தயவுசெய்து உதவுங்கள்.",
                wordAudioURL: nil,
                exampleAudioURL: nil
            ),
            LessonVocabularyItem(
                word: "மன்னிக்கவும்",
                translation: "Excuse me/Sorry",
                pronunciation: "/mannikkavum/",
                partOfSpeech: "interjection",
                example: "மன்னிக்கவும், எனக்கு புரியவில்லை.",
                wordAudioURL: "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_1_vocab_9_word.mp3",
                exampleAudioURL: "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_1_vocab_9_example.mp3"
            ),
            LessonVocabularyItem(
                word: "காலை வணக்கம்",
                translation: "Good morning",
                pronunciation: "/kālai vaṇakkam/",
                partOfSpeech: "phrase",
                example: "காலை வணக்கம், எப்படி இருக்கிறீர்கள்?",
                wordAudioURL: nil,
                exampleAudioURL: nil
            ),
            LessonVocabularyItem(
                word: "மாலை வணக்கம்",
                translation: "Good evening",
                pronunciation: "/mālai vaṇakkam/",
                partOfSpeech: "phrase",
                example: "மாலை வணக்கம், நல்லா இருக்கிறீங்களா?",
                wordAudioURL: nil,
                exampleAudioURL: nil
            )
        ]
    }

    private func getFallbackDialogues() -> [TileItem] {
        return [
            TileItem(title: "How are you?", subtitle: "Common greeting", color: .emojiPurple, onTap: { showingConversationDetail = true }),
            TileItem(title: "What's your name?", subtitle: "Introduction", color: .emojiPurple, onTap: { showingConversationDetail = true }),
            TileItem(title: "Nice to meet you", subtitle: "First meeting", color: .emojiPurple, onTap: { showingConversationDetail = true }),
            TileItem(title: "See you later", subtitle: "Farewell", color: .emojiPurple, onTap: { showingConversationDetail = true }),
            TileItem(title: "Where are you from?", subtitle: "Origin question", color: .emojiPurple, onTap: { showingConversationDetail = true }),
            TileItem(title: "I'm from...", subtitle: "Origin response", color: .emojiPurple, onTap: { showingConversationDetail = true }),
            TileItem(title: "What do you do?", subtitle: "Job question", color: .emojiPurple, onTap: { showingConversationDetail = true }),
            TileItem(title: "I work as...", subtitle: "Job response", color: .emojiPurple, onTap: { showingConversationDetail = true })
        ]
    }

    private func getFallbackGrammar() -> [TileItem] {
        return [
            TileItem(title: "Present Tense", subtitle: "Current actions", color: .emojiBlue),
            TileItem(title: "Articles", subtitle: "A, an, the", color: .emojiBlue),
            TileItem(title: "Pronouns", subtitle: "I, you, he, she", color: .emojiBlue),
            TileItem(title: "Word Order", subtitle: "Subject-Verb-Object", color: .emojiBlue),
            TileItem(title: "Past Tense", subtitle: "Completed actions", color: .emojiBlue),
            TileItem(title: "Future Tense", subtitle: "Upcoming actions", color: .emojiBlue),
            TileItem(title: "Questions", subtitle: "How to ask", color: .emojiBlue),
            TileItem(title: "Negation", subtitle: "How to say no", color: .emojiBlue)
        ]
    }

    private func getFallbackExercises() -> [TileItem] {
        return [
            TileItem(title: "Fill in the blanks", subtitle: "Complete sentences", color: .emojiOrange),
            TileItem(title: "Multiple choice", subtitle: "Choose correct answer", color: .emojiOrange),
            TileItem(title: "Translation", subtitle: "Translate phrases", color: .emojiOrange),
            TileItem(title: "Pronunciation", subtitle: "Practice speaking", color: .emojiOrange),
            TileItem(title: "Matching", subtitle: "Connect related items", color: .emojiOrange),
            TileItem(title: "Listening", subtitle: "Audio comprehension", color: .emojiOrange),
            TileItem(title: "Writing", subtitle: "Compose sentences", color: .emojiOrange),
            TileItem(title: "Speaking", subtitle: "Oral practice", color: .emojiOrange)
        ]
    }
}

// MARK: - Supporting Components

struct LessonStatItem: View {
    let icon: String
    let value: String
    let label: String
    let color: Color

    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(color)

            Text(value)
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(.primary)

            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}



struct PremiumContentCard<Content: View>: View {
    let title: String
    let icon: String
    let color: Color
    let content: () -> Content

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(color)
                    .frame(width: 40, height: 40)
                    .background(color.opacity(0.1))
                    .cornerRadius(12)

                Text(title)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)

                Spacer()
            }

            content()
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

struct LessonVocabularyCard: View {
    let word: String
    let translation: String?
    let pronunciation: String?

    var body: some View {
        HStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 4) {
                Text(word)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.primary)

                if let translation = translation {
                    Text(translation)
                        .font(.system(size: 16))
                        .foregroundColor(.secondary)
                }

                if let pronunciation = pronunciation {
                    Text("[\(pronunciation)]")
                        .font(.system(size: 14))
                        .foregroundColor(.green)
                        .italic()
                }
            }

            Spacer()

            Button(action: {}) {
                Image(systemName: "speaker.wave.2.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.blue)
                    .padding(12)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(12)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6).opacity(0.5))
        )
    }
}

struct LessonDialogueCard: View {
    let speaker: String
    let text: String
    let translation: String?

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(speaker)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.purple)

                Spacer()

                Button(action: {}) {
                    Image(systemName: "speaker.wave.2.fill")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.purple)
                }
            }

            Text(text)
                .font(.system(size: 16))
                .foregroundColor(.primary)

            if let translation = translation {
                Text(translation)
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .italic()
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.purple.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.purple.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

struct LessonExerciseCard: View {
    let exercise: SupabaseExercise

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(exercise.question)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.primary)

            if let options = exercise.options, !options.isEmpty {
                VStack(spacing: 8) {
                    ForEach(Array(options.enumerated()), id: \.offset) { index, option in
                        HStack {
                            Text("\(index + 1).")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.red)

                            Text(option)
                                .font(.system(size: 14))
                                .foregroundColor(.primary)

                            Spacer()
                        }
                        .padding(.vertical, 4)
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.red.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.red.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

// MARK: - Data Models for Extracted Content

struct LessonVocabularyItem {
    let word: String
    let translation: String?
    let pronunciation: String?
    let partOfSpeech: String?
    let example: String?
    let wordAudioURL: String?
    let exampleAudioURL: String?
}

struct LessonDialogueItem {
    let speaker: String
    let text: String
    let translation: String?
    let culturalNote: String?
    let audioURL: String?
}

struct LessonExerciseItem {
    let type: String
    let question: String
    let options: [String]
    let correctAnswer: Int
    let explanation: String?
    let points: Int
}

struct LessonGrammarPoint {
    let rule: String
    let explanation: String
    let examples: [String]
    let tips: String?
}

struct ExtractedExerciseCard: View {
    let exercise: LessonExerciseItem
    let index: Int

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Exercise \(index)")
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.red)
                    .cornerRadius(8)

                Spacer()

                Text("\(exercise.points) pts")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.red)
            }

            Text(exercise.question)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.primary)

            if !exercise.options.isEmpty {
                VStack(spacing: 8) {
                    ForEach(Array(exercise.options.enumerated()), id: \.offset) { index, option in
                        HStack {
                            Text("\(index + 1).")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.red)
                                .frame(width: 20, alignment: .leading)

                            Text(option)
                                .font(.system(size: 14))
                                .foregroundColor(.primary)

                            Spacer()

                            if index == exercise.correctAnswer {
                                Image(systemName: "checkmark.circle.fill")
                                    .font(.system(size: 14))
                                    .foregroundColor(.green)
                            }
                        }
                        .padding(.vertical, 4)
                    }
                }
            }

            if let explanation = exercise.explanation {
                Text("💡 \(explanation)")
                    .font(.system(size: 13))
                    .foregroundColor(.secondary)
                    .italic()
                    .padding(.top, 8)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.red.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.red.opacity(0.2), lineWidth: 1)
                )
        )
    }
}



#Preview {
    let sampleLesson = SupabaseLesson(
        id: UUID(),
        pathId: UUID(),
        title: "Saying how you're doing",
        description: "Learn essential phrases for expressing how you feel",
        lessonType: "conversation",
        difficultyLevel: 2,
        estimatedDuration: 25,
        sequenceOrder: 1,
        learningObjectives: ["Express feelings", "Use common phrases", "Practice pronunciation"],
        vocabularyFocus: ["good", "fine", "tired", "happy"],
        grammarConcepts: ["Present tense"],
        culturalNotes: "Different cultures express feelings differently",
        prerequisiteLessons: [],
        contentMetadata: SupabaseAnyCodable([
            "vocabulary": [
                [
                    "word": "good",
                    "translation": "bien",
                    "pronunciation": "bee-en"
                ],
                [
                    "word": "tired",
                    "translation": "cansado",
                    "pronunciation": "kan-sa-do"
                ]
            ],
            "dialogues": [
                [
                    "speaker": "Maria",
                    "text": "How are you doing?",
                    "translation": "¿Cómo estás?"
                ],
                [
                    "speaker": "Juan",
                    "text": "I'm doing well, thanks",
                    "translation": "Estoy bien, gracias"
                ]
            ],
            "exercises": [
                [
                    "type": "multiple_choice",
                    "question": "How do you say 'good' in Spanish?",
                    "options": ["bien", "mal", "así", "todo"]
                ]
            ],
            "grammar_points": [
                [
                    "rule": "Present tense of 'estar'",
                    "explanation": "Used to express temporary states"
                ]
            ]
        ]),
        isActive: true,
        createdAt: Date(),
        updatedAt: Date(),
        audioUrl: nil,
        hasAudio: false,
        audioMetadata: nil
    )

    LessonDetailView(lesson: sampleLesson)
        .preferredColorScheme(.dark)
}