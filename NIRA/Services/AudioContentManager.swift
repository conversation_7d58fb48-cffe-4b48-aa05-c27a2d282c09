import Foundation
import SwiftUI

@MainActor
class AudioContentManager: ObservableObject {
    static let shared = AudioContentManager()
    
    @Published var isProcessingLesson = false
    @Published var processingProgress: Double = 0.0
    @Published var processingStatus: String = ""
    @Published var availableAudioContent: [String: [String]] = [:]
    
    private let audioPlayerService = AudioPlayerService.shared
    private let elevenLabsService = ElevenLabsService.shared
    private let supabaseClient = NIRASupabaseClient.shared
    
    private init() {
        loadAvailableAudioContent()
    }
    
    // MARK: - Public Methods
    
    func playAudioWithFallback(text: String, audioURL: String?, context: String = "") async {
        // Try to play existing audio first
        if let audioURL = audioURL, !audioURL.isEmpty {
            print("🔊 Playing existing audio: \(audioURL)")
            audioPlayerService.playAudio(from: audioURL)
            return
        }
        
        // Generate audio if not available
        print("🎵 No audio available for '\(text)', generating...")
        await generateAndPlayAudio(text: text, context: context)
    }
    
    func generateAndPlayAudio(text: String, context: String = "") async {
        do {
            processingStatus = "Generating audio for: \(text.prefix(30))..."
            let audioURL = try await elevenLabsService.generateAudio(text: text)
            
            // Play the generated audio
            audioPlayerService.playAudio(from: audioURL.path)
            
            processingStatus = "Audio generated successfully"
            
            // Upload to Supabase for future use
            await uploadGeneratedAudio(audioURL: audioURL, text: text, context: context)
            
        } catch {
            processingStatus = "Failed to generate audio: \(error.localizedDescription)"
            print("❌ Audio generation failed: \(error)")
        }
    }
    
    func processLessonAudio(_ lesson: SupabaseLesson) async {
        isProcessingLesson = true
        processingProgress = 0.0
        processingStatus = "Processing lesson audio..."
        
        defer {
            Task { @MainActor in
                isProcessingLesson = false
                processingProgress = 0.0
                processingStatus = ""
            }
        }
        
        do {
            let generatedAudio = try await elevenLabsService.generateMissingAudio(for: lesson)
            
            processingStatus = "Uploading generated audio..."
            processingProgress = 0.8
            
            // Upload generated audio to Supabase
            for (key, audioURL) in generatedAudio {
                await uploadGeneratedAudio(audioURL: audioURL, text: key, context: "lesson_\(lesson.id)")
            }
            
            processingProgress = 1.0
            processingStatus = "Lesson audio processing complete"
            
            // Update available audio content
            loadAvailableAudioContent()
            
        } catch {
            processingStatus = "Failed to process lesson audio: \(error.localizedDescription)"
            print("❌ Lesson audio processing failed: \(error)")
        }
    }
    
    func getAudioURL(for text: String, context: String = "") -> String? {
        // Check if we have a cached or uploaded audio file for this text
        let key = generateAudioKey(text: text, context: context)
        return availableAudioContent[context]?.first { $0.contains(key) }
    }
    
    func hasAudio(for text: String, context: String = "") -> Bool {
        return getAudioURL(for: text, context: context) != nil
    }
    
    // MARK: - Private Methods
    
    private func loadAvailableAudioContent() {
        // Load available audio content from cache and Supabase
        Task {
            // This would typically query Supabase for available audio files
            // For now, we'll use a simple cache-based approach
            await updateAvailableAudioFromCache()
        }
    }
    
    private func updateAvailableAudioFromCache() async {
        let cacheDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            .appendingPathComponent("AudioCache")
        
        do {
            let audioFiles = try FileManager.default.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
            let audioFilenames = audioFiles.map { $0.lastPathComponent }
            
            await MainActor.run {
                availableAudioContent["cache"] = audioFilenames
            }
        } catch {
            print("❌ Failed to load cached audio content: \(error)")
        }
    }
    
    private func uploadGeneratedAudio(audioURL: URL, text: String, context: String) async {
        // This would upload the generated audio to Supabase Storage
        // For now, we'll just log the action
        print("📤 Would upload audio to Supabase: \(audioURL.lastPathComponent) for '\(text)'")
        
        // In a full implementation, this would:
        // 1. Upload the audio file to Supabase Storage
        // 2. Update the lesson content with the new audio URL
        // 3. Cache the mapping locally
    }
    
    private func generateAudioKey(text: String, context: String) -> String {
        let cleanText = text.lowercased()
            .replacingOccurrences(of: " ", with: "_")
            .replacingOccurrences(of: "[^a-z0-9_]", with: "", options: .regularExpression)
        return "\(context)_\(cleanText)".prefix(50).description
    }
}

// MARK: - Enhanced Audio Button with Fallback

struct EnhancedAudioButton: View {
    let text: String
    let audioURL: String?
    let context: String
    let size: CGFloat
    let color: Color
    
    @StateObject private var audioManager = AudioContentManager.shared
    @StateObject private var audioService = AudioPlayerService.shared
    
    init(text: String, audioURL: String?, context: String = "", size: CGFloat = 32, color: Color = .blue) {
        self.text = text
        self.audioURL = audioURL
        self.context = context
        self.size = size
        self.color = color
    }
    
    var body: some View {
        Button(action: {
            Task {
                await audioManager.playAudioWithFallback(text: text, audioURL: audioURL, context: context)
            }
        }) {
            Group {
                if isCurrentlyLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: color))
                } else if isCurrentlyPlaying {
                    Image(systemName: "stop.fill")
                        .font(.system(size: size * 0.6, weight: .medium))
                        .foregroundColor(color)
                } else {
                    VStack(spacing: 2) {
                        Image(systemName: hasAudioAvailable ? "speaker.wave.2.fill" : "waveform.badge.plus")
                            .font(.system(size: size * 0.5, weight: .medium))
                            .foregroundColor(hasAudioAvailable ? color : .orange)
                        
                        if !hasAudioAvailable {
                            Text("Gen")
                                .font(.system(size: size * 0.2, weight: .bold))
                                .foregroundColor(.orange)
                        }
                    }
                }
            }
            .frame(width: size, height: size)
            .background(
                Circle()
                    .fill(hasAudioAvailable ? color.opacity(0.1) : Color.orange.opacity(0.1))
            )
            .scaleEffect(isCurrentlyPlaying ? 1.1 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isCurrentlyPlaying)
        }
        .disabled(text.isEmpty || isCurrentlyLoading)
    }
    
    private var hasAudioAvailable: Bool {
        return audioURL != nil && !audioURL!.isEmpty
    }
    
    private var isCurrentlyPlaying: Bool {
        return audioService.isPlaying && (
            audioService.currentAudioURL == audioURL ||
            audioService.currentAudioURL?.contains(text.prefix(10)) == true
        )
    }
    
    private var isCurrentlyLoading: Bool {
        return audioService.isLoading || audioManager.isProcessingLesson
    }
}

// MARK: - Lesson Audio Processing View

struct LessonAudioProcessingView: View {
    let lesson: SupabaseLesson
    @StateObject private var audioManager = AudioContentManager.shared
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Header
                VStack(spacing: 8) {
                    Image(systemName: "waveform.circle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)
                    
                    Text("Audio Processing")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("Generating missing audio for lesson content")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                // Progress
                if audioManager.isProcessingLesson {
                    VStack(spacing: 16) {
                        ProgressView(value: audioManager.processingProgress)
                            .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                            .scaleEffect(1.2)
                        
                        Text(audioManager.processingStatus)
                            .font(.body)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.center)
                        
                        Text("\(Int(audioManager.processingProgress * 100))% Complete")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
                } else {
                    VStack(spacing: 16) {
                        Text("Ready to process lesson audio")
                            .font(.body)
                            .foregroundColor(.secondary)
                        
                        Button("Start Processing") {
                            Task {
                                await audioManager.processLessonAudio(lesson)
                            }
                        }
                        .buttonStyle(.borderedProminent)
                        .controlSize(.large)
                    }
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("Audio Generation")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}
