import Foundation
import SwiftUI

@MainActor
class TamilAudioGenerationService: ObservableObject {
    static let shared = TamilAudioGenerationService()
    
    @Published var isGenerating = false
    @Published var generationProgress: Double = 0.0
    @Published var generationStatus = ""
    @Published var generatedCount = 0
    @Published var totalCount = 0
    
    private let elevenLabsService = ElevenLabsService.shared
    private let supabaseClient = SupabaseClient.shared
    
    // Voice IDs for Tamil content (user preferred)
    private let voiceIDs = [
        "jsCqWAovK2LkecY7zXl4", // Freya
        "EXAVITQu4vr4xnSDxMaL"  // Elli (Bella)
    ]
    
    private init() {}
    
    // MARK: - Main Generation Function
    
    func generateAllTamilAudio(for lessonId: String = "b966c742-d36d-4d94-9e35-7c17a5039487") async {
        isGenerating = true
        generationProgress = 0.0
        generatedCount = 0
        
        defer {
            isGenerating = false
            generationProgress = 0.0
        }
        
        do {
            // 1. Fetch lesson content
            generationStatus = "Fetching lesson content..."
            guard let lesson = await fetchLessonContent(lessonId: lessonId) else {
                generationStatus = "Failed to fetch lesson content"
                return
            }
            
            // 2. Extract all Tamil content
            generationStatus = "Extracting Tamil content..."
            let tamilContent = extractTamilContent(from: lesson)
            totalCount = tamilContent.count
            
            print("🎯 Found \(totalCount) Tamil text items to generate audio for")
            
            // 3. Generate audio for each Tamil text
            var updatedContent = lesson
            
            for (index, item) in tamilContent.enumerated() {
                generationStatus = "Generating audio \(index + 1)/\(totalCount): \(item.text.prefix(30))..."
                generationProgress = Double(index) / Double(totalCount)
                
                do {
                    let audioURL = try await generateAndUploadAudio(
                        text: item.text,
                        filename: item.filename,
                        voiceIndex: index % voiceIDs.count
                    )
                    
                    // Update the content with audio URL
                    updatedContent = updateContentWithAudioURL(
                        content: updatedContent,
                        item: item,
                        audioURL: audioURL
                    )
                    
                    generatedCount += 1
                    print("✅ Generated audio \(generatedCount)/\(totalCount): \(item.filename)")
                    
                } catch {
                    print("❌ Failed to generate audio for '\(item.text)': \(error)")
                }
                
                // Small delay to respect rate limits
                try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
            }
            
            // 4. Update lesson in database
            generationStatus = "Updating lesson in database..."
            generationProgress = 0.95
            
            await updateLessonInDatabase(lessonId: lessonId, content: updatedContent)
            
            generationProgress = 1.0
            generationStatus = "✅ Generated \(generatedCount)/\(totalCount) audio files successfully!"
            
        } catch {
            generationStatus = "❌ Generation failed: \(error.localizedDescription)"
            print("❌ Tamil audio generation failed: \(error)")
        }
    }
    
    // MARK: - Content Extraction
    
    private func extractTamilContent(from lesson: [String: Any]) -> [TamilContentItem] {
        var items: [TamilContentItem] = []
        
        // Extract vocabulary
        if let vocabulary = lesson["vocabulary"] as? [[String: Any]] {
            for (index, vocab) in vocabulary.enumerated() {
                if let word = vocab["word"] as? String {
                    items.append(TamilContentItem(
                        text: word,
                        type: .vocabularyWord,
                        index: index,
                        filename: "vocab_\(String(format: "%02d", index + 1))_word.mp3",
                        contentPath: "vocabulary.\(index).word_audio_url"
                    ))
                }
                
                if let example = vocab["example"] as? String {
                    items.append(TamilContentItem(
                        text: example,
                        type: .vocabularyExample,
                        index: index,
                        filename: "vocab_\(String(format: "%02d", index + 1))_example.mp3",
                        contentPath: "vocabulary.\(index).example_audio_url"
                    ))
                }
            }
        }
        
        // Extract conversations
        if let conversations = lesson["conversations"] as? [[String: Any]] {
            for (convIndex, conversation) in conversations.enumerated() {
                if let exchanges = conversation["exchanges"] as? [[String: Any]] {
                    for (exIndex, exchange) in exchanges.enumerated() {
                        if let text = exchange["text"] as? String {
                            items.append(TamilContentItem(
                                text: text,
                                type: .conversation,
                                index: convIndex,
                                subIndex: exIndex,
                                filename: "conv_\(String(format: "%02d", convIndex + 1))_\(String(format: "%02d", exIndex + 1)).mp3",
                                contentPath: "conversations.\(convIndex).exchanges.\(exIndex).audio_url"
                            ))
                        }
                    }
                }
            }
        }
        
        // Extract grammar examples
        if let grammarPoints = lesson["grammar_points"] as? [[String: Any]] {
            for (grammarIndex, grammar) in grammarPoints.enumerated() {
                if let examples = grammar["examples"] as? [String] {
                    for (exampleIndex, example) in examples.enumerated() {
                        // Extract only Tamil part (before the English translation)
                        let tamilText = extractTamilFromExample(example)
                        if !tamilText.isEmpty {
                            items.append(TamilContentItem(
                                text: tamilText,
                                type: .grammarExample,
                                index: grammarIndex,
                                subIndex: exampleIndex,
                                filename: "grammar_\(String(format: "%02d", grammarIndex + 1))_\(String(format: "%02d", exampleIndex + 1)).mp3",
                                contentPath: "grammar_points.\(grammarIndex).examples_audio_urls.\(exampleIndex)"
                            ))
                        }
                    }
                }
            }
        }
        
        // Extract exercise options (Tamil text)
        if let exercises = lesson["exercises"] as? [[String: Any]] {
            for (exerciseIndex, exercise) in exercises.enumerated() {
                if let options = exercise["options"] as? [String] {
                    for (optionIndex, option) in options.enumerated() {
                        // Only generate audio for Tamil text (contains Tamil characters)
                        if containsTamilText(option) {
                            items.append(TamilContentItem(
                                text: option,
                                type: .exerciseOption,
                                index: exerciseIndex,
                                subIndex: optionIndex,
                                filename: "exercise_\(String(format: "%02d", exerciseIndex + 1))_option_\(String(format: "%02d", optionIndex + 1)).mp3",
                                contentPath: "exercises.\(exerciseIndex).options_audio_urls.\(optionIndex)"
                            ))
                        }
                    }
                }
            }
        }
        
        return items
    }
    
    private func extractTamilFromExample(_ example: String) -> String {
        // Extract Tamil text before any English translation
        // Format: "Tamil text (romanization) - English translation"
        let components = example.components(separatedBy: " - ")
        if let tamilPart = components.first {
            // Remove romanization in parentheses
            let tamilOnly = tamilPart.replacingOccurrences(of: "\\s*\\([^)]*\\)", with: "", options: .regularExpression)
            return tamilOnly.trimmingCharacters(in: .whitespacesAndNewlines)
        }
        return ""
    }
    
    private func containsTamilText(_ text: String) -> Bool {
        // Check if text contains Tamil Unicode characters
        let tamilRange = "\u{0B80}"..."\u{0BFF}"
        return text.unicodeScalars.contains { tamilRange.contains($0) }
    }
    
    // MARK: - Audio Generation and Upload
    
    private func generateAndUploadAudio(text: String, filename: String, voiceIndex: Int) async throws -> String {
        // Generate audio using ElevenLabs
        let audioURL = try await elevenLabsService.generateAudio(text: text, filename: filename)
        
        // Upload to Supabase Storage
        let publicURL = try await uploadToSupabaseStorage(audioURL: audioURL, filename: filename)
        
        return publicURL
    }
    
    private func uploadToSupabaseStorage(audioURL: URL, filename: String) async throws -> String {
        // Read audio file data
        let audioData = try Data(contentsOf: audioURL)
        
        // Upload to Supabase Storage
        let storagePath = "tamil/a1/animals_nature/\(filename)"
        let uploadURL = "\(supabaseClient.supabaseURL)/storage/v1/object/lesson-audio/\(storagePath)"
        
        var request = URLRequest(url: URL(string: uploadURL)!)
        request.httpMethod = "POST"
        request.setValue("Bearer \(supabaseClient.supabaseKey)", forHTTPHeaderField: "Authorization")
        request.setValue("audio/mpeg", forHTTPHeaderField: "Content-Type")
        request.httpBody = audioData
        
        let (_, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 || httpResponse.statusCode == 201 else {
            throw TamilAudioError.uploadFailed
        }
        
        // Return public URL
        let publicURL = "\(supabaseClient.supabaseURL)/storage/v1/object/public/lesson-audio/\(storagePath)"
        print("✅ Uploaded to Supabase: \(publicURL)")
        
        return publicURL
    }
    
    // MARK: - Database Operations
    
    private func fetchLessonContent(lessonId: String) async -> [String: Any]? {
        // This would fetch from Supabase - for now return the known content
        // In production, implement proper Supabase query
        return nil // Will be implemented with actual Supabase call
    }
    
    private func updateContentWithAudioURL(content: [String: Any], item: TamilContentItem, audioURL: String) -> [String: Any] {
        var updatedContent = content
        
        // Update the content at the specified path with the audio URL
        // This is a simplified version - in production, implement proper JSON path updating
        
        return updatedContent
    }
    
    private func updateLessonInDatabase(lessonId: String, content: [String: Any]) async {
        // Update lesson content_metadata in Supabase
        // Implementation will be added
    }
}

// MARK: - Supporting Types

struct TamilContentItem {
    let text: String
    let type: TamilContentType
    let index: Int
    let subIndex: Int?
    let filename: String
    let contentPath: String
    
    init(text: String, type: TamilContentType, index: Int, subIndex: Int? = nil, filename: String, contentPath: String) {
        self.text = text
        self.type = type
        self.index = index
        self.subIndex = subIndex
        self.filename = filename
        self.contentPath = contentPath
    }
}

enum TamilContentType {
    case vocabularyWord
    case vocabularyExample
    case conversation
    case grammarExample
    case exerciseOption
}

enum TamilAudioError: LocalizedError {
    case uploadFailed
    case contentExtractionFailed
    
    var errorDescription: String? {
        switch self {
        case .uploadFailed:
            return "Failed to upload audio to Supabase Storage"
        case .contentExtractionFailed:
            return "Failed to extract Tamil content from lesson"
        }
    }
}
