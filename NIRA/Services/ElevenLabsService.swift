import Foundation
import AVFoundation

@MainActor
class ElevenLabsService: ObservableObject {
    static let shared = ElevenLabsService()
    
    @Published var isGenerating = false
    @Published var generationProgress: Double = 0.0
    @Published var generationError: String?
    @Published var lastGeneratedAudio: String?
    
    private let apiKey = APIKeys.elevenLabsAPIKey
    private let baseURL = "https://api.elevenlabs.io/v1"
    private let tamilVoiceID = "9BWtsMINqrJLrRacOk9x" // Tamil voice ID
    
    // Audio cache directory (same as AudioPlayerService)
    private let cacheDirectory: URL
    private let fileManager = FileManager.default
    
    private init() {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        self.cacheDirectory = documentsPath.appendingPathComponent("AudioCache")
        createCacheDirectory()
    }
    
    private func createCacheDirectory() {
        if !fileManager.fileExists(atPath: cacheDirectory.path) {
            do {
                try fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
                print("✅ Created ElevenLabs audio cache directory: \(cacheDirectory.path)")
            } catch {
                print("❌ Failed to create cache directory: \(error)")
            }
        }
    }
    
    // MARK: - Public Methods
    
    func generateAudio(text: String, filename: String? = nil) async throws -> URL {
        guard !apiKey.isEmpty && !apiKey.contains("sk_b8b8c8e8") else {
            throw ElevenLabsError.invalidAPIKey
        }
        
        await MainActor.run {
            isGenerating = true
            generationProgress = 0.0
            generationError = nil
        }
        
        defer {
            Task { @MainActor in
                isGenerating = false
                generationProgress = 0.0
            }
        }
        
        do {
            let audioData = try await generateAudioData(text: text)
            let audioURL = try await saveAudioData(audioData, filename: filename ?? generateFilename(for: text))
            
            await MainActor.run {
                lastGeneratedAudio = audioURL.path
                generationProgress = 1.0
            }
            
            return audioURL
        } catch {
            await MainActor.run {
                generationError = error.localizedDescription
            }
            throw error
        }
    }
    
    func generateMissingAudio(for lesson: SupabaseLesson) async throws -> [String: URL] {
        var generatedAudioURLs: [String: URL] = [:]
        
        guard let contentMetadata = lesson.contentMetadata?.value as? [String: Any] else {
            throw ElevenLabsError.invalidLessonContent
        }
        
        await MainActor.run {
            isGenerating = true
            generationProgress = 0.0
        }
        
        defer {
            Task { @MainActor in
                isGenerating = false
                generationProgress = 0.0
            }
        }
        
        // Generate vocabulary audio
        if let vocabulary = contentMetadata["vocabulary"] as? [[String: Any]] {
            let vocabCount = vocabulary.count
            for (index, vocab) in vocabulary.enumerated() {
                if let word = vocab["word"] as? String {
                    let filename = "lesson_\(lesson.id.uuidString)_vocab_\(index + 1)_word.mp3"
                    if !audioFileExists(filename: filename) {
                        let audioURL = try await generateAudio(text: word, filename: filename)
                        generatedAudioURLs["vocab_\(index + 1)_word"] = audioURL
                    }
                }
                
                if let example = vocab["example"] as? String {
                    let filename = "lesson_\(lesson.id.uuidString)_vocab_\(index + 1)_example.mp3"
                    if !audioFileExists(filename: filename) {
                        let audioURL = try await generateAudio(text: example, filename: filename)
                        generatedAudioURLs["vocab_\(index + 1)_example"] = audioURL
                    }
                }
                
                await MainActor.run {
                    generationProgress = Double(index + 1) / Double(vocabCount) * 0.7 // 70% for vocabulary
                }
            }
        }
        
        // Generate conversation audio
        if let conversations = contentMetadata["conversations"] as? [[String: Any]] {
            for (convIndex, conversation) in conversations.enumerated() {
                if let exchanges = conversation["exchanges"] as? [[String: Any]] {
                    for (exIndex, exchange) in exchanges.enumerated() {
                        if let text = exchange["text"] as? String {
                            let filename = "lesson_\(lesson.id.uuidString)_conv_\(convIndex + 1)_ex_\(exIndex + 1).mp3"
                            if !audioFileExists(filename: filename) {
                                let audioURL = try await generateAudio(text: text, filename: filename)
                                generatedAudioURLs["conv_\(convIndex + 1)_ex_\(exIndex + 1)"] = audioURL
                            }
                        }
                    }
                }
            }
            
            await MainActor.run {
                generationProgress = 0.9 // 90% after conversations
            }
        }
        
        await MainActor.run {
            generationProgress = 1.0
        }
        
        return generatedAudioURLs
    }
    
    // MARK: - Private Methods
    
    private func generateAudioData(text: String) async throws -> Data {
        guard let url = URL(string: "\(baseURL)/text-to-speech/\(tamilVoiceID)") else {
            throw ElevenLabsError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("audio/mpeg", forHTTPHeaderField: "Accept")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(apiKey, forHTTPHeaderField: "xi-api-key")
        
        let requestBody: [String: Any] = [
            "text": text,
            "model_id": "eleven_multilingual_v2",
            "voice_settings": [
                "stability": 0.5,
                "similarity_boost": 0.75,
                "style": 0.0,
                "use_speaker_boost": true
            ]
        ]
        
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw ElevenLabsError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            throw ElevenLabsError.apiError(httpResponse.statusCode)
        }
        
        return data
    }
    
    private func saveAudioData(_ data: Data, filename: String) async throws -> URL {
        let audioURL = cacheDirectory.appendingPathComponent(filename)
        try data.write(to: audioURL)
        print("✅ Generated and saved audio: \(filename)")
        return audioURL
    }
    
    private func generateFilename(for text: String) -> String {
        let timestamp = Int(Date().timeIntervalSince1970)
        let cleanText = text.prefix(20).replacingOccurrences(of: " ", with: "_")
        return "generated_\(cleanText)_\(timestamp).mp3"
    }
    
    private func audioFileExists(filename: String) -> Bool {
        let audioURL = cacheDirectory.appendingPathComponent(filename)
        return fileManager.fileExists(atPath: audioURL.path)
    }
}

// MARK: - Error Types

enum ElevenLabsError: LocalizedError {
    case invalidAPIKey
    case invalidURL
    case invalidResponse
    case invalidLessonContent
    case apiError(Int)
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidAPIKey:
            return "Invalid ElevenLabs API key. Please check your configuration."
        case .invalidURL:
            return "Invalid ElevenLabs API URL."
        case .invalidResponse:
            return "Invalid response from ElevenLabs API."
        case .invalidLessonContent:
            return "Invalid lesson content structure."
        case .apiError(let code):
            return "ElevenLabs API error: \(code)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }
}

// MARK: - Audio Generation Button Component

struct AudioGenerationButton: View {
    let text: String
    let onAudioGenerated: (URL) -> Void
    
    @StateObject private var elevenLabsService = ElevenLabsService.shared
    @State private var isGenerating = false
    
    var body: some View {
        Button(action: {
            Task {
                await generateAudio()
            }
        }) {
            HStack(spacing: 8) {
                if isGenerating {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                } else {
                    Image(systemName: "waveform.badge.plus")
                        .font(.system(size: 16, weight: .medium))
                }
                
                Text(isGenerating ? "Generating..." : "Generate Audio")
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isGenerating ? Color.gray : Color.blue)
            )
        }
        .disabled(isGenerating || text.isEmpty)
    }
    
    private func generateAudio() async {
        isGenerating = true
        
        do {
            let audioURL = try await elevenLabsService.generateAudio(text: text)
            onAudioGenerated(audioURL)
        } catch {
            print("❌ Audio generation failed: \(error)")
        }
        
        isGenerating = false
    }
}
