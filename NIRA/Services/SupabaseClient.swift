import Foundation
import Combine
import Supabase

// MARK: - Configuration
// Using centralized API configuration

@MainActor
class NIRASupabaseClient: ObservableObject {
    static let shared = NIRASupabaseClient()

    let client: SupabaseClient
    @Published var session: Session?
    @Published var currentUser: Auth.User?
    @Published var isConnected = false
    @Published var lastError: Error?

    private var configuredURL: String = ""

    private init() {
        // Initialize Supabase client with secure configuration
        let supabaseURL: String
        let supabaseKey: String

        // Try to get from SecureAPIKeys first, fallback to hardcoded APIKeys
        let secureURL = SecureAPIKeys.supabaseURL
        let secureKey = SecureAPIKeys.supabaseAnonKey

        print("🔍 NIRASupabaseClient init - SecureAPIKeys check:")
        print("   URL: '\(secureURL.isEmpty ? "EMPTY" : secureURL)'")
        print("   Key: '\(secureKey.isEmpty ? "EMPTY" : String(secureKey.prefix(20)))...'")

        if !secureURL.isEmpty && !secureKey.isEmpty &&
           !secureURL.contains("PLACEHOLDER") && !secureKey.contains("PLACEHOLDER") {
            supabaseURL = secureURL
            supabaseKey = secureKey
            print("✅ Using secure API keys from keychain")
        } else {
            // Fallback to hardcoded APIKeys
            print("⚠️ SecureAPIKeys not available, falling back to hardcoded APIKeys")
            print("   Reason: URL empty=\(secureURL.isEmpty), Key empty=\(secureKey.isEmpty)")
            print("   URL contains PLACEHOLDER=\(secureURL.contains("PLACEHOLDER")), Key contains PLACEHOLDER=\(secureKey.contains("PLACEHOLDER"))")
            
            // Use hardcoded APIKeys as fallback
            if APIKeys.isConfigured {
                supabaseURL = APIKeys.supabaseURL
                supabaseKey = APIKeys.supabaseAnonKey
                print("✅ Using hardcoded API keys from APIKeys.swift")
            } else {
                // Last resort - use placeholder values that won't crash
                print("⚠️ Using development mode - Supabase features will be limited")
                supabaseURL = "https://placeholder.supabase.co"
                supabaseKey = "placeholder_key"
            }
        }

        guard let url = URL(string: supabaseURL) else {
            print("❌ Invalid Supabase URL: \(supabaseURL)")
            // Use a valid placeholder URL to prevent crash
            let fallbackURL = URL(string: "https://placeholder.supabase.co")!
            self.client = SupabaseClient(
                supabaseURL: fallbackURL,
                supabaseKey: "placeholder_key"
            )
            return
        }

        self.client = SupabaseClient(
            supabaseURL: url,
            supabaseKey: supabaseKey
        )

        // Store the configured URL for later checks
        self.configuredURL = supabaseURL

        // Start background tasks
        self.startBackgroundTasks()
    }

    private func startBackgroundTasks() {
        setupAuthListener()
        checkConnection()
    }

    // MARK: - Authentication

    func signUp(email: String, password: String) async throws {
        try await client.auth.signUp(email: email, password: password)
        // The auth state listener will handle updating session and user
    }

    func signIn(email: String, password: String) async throws {
        try await client.auth.signIn(email: email, password: password)
        // The auth state listener will handle updating session and user
    }

    func signOut() async throws {
        try await client.auth.signOut()
        self.session = nil
        self.currentUser = nil
    }

    func resetPassword(email: String) async throws {
        try await client.auth.resetPasswordForEmail(email)
    }

    // MARK: - Database Operations

    func getLessons(language: String? = nil, level: String? = nil) async throws -> [SupabaseLesson] {
        // If no language specified, return empty array
        guard let language = language else {
            print("⚠️ No language specified for lesson query")
            return []
        }

        // Convert language name to database code
        let languageCode = getLanguageCode(for: language)
        print("🔍 Converting language '\(language)' to code '\(languageCode)'")

        // First get the language ID
        let languageResponse: [SupabaseLanguageModel] = try await client
            .from("languages")
            .select()
            .eq("code", value: languageCode)
            .execute()
            .value

        guard let languageId = languageResponse.first?.id else {
            print("⚠️ Language '\(language)' not found in database")
            return []
        }

        // Get learning paths for this language
        let pathsResponse: [SupabaseLearningPath] = try await client
            .from("learning_paths")
            .select()
            .eq("language_id", value: languageId.uuidString)
            .eq("is_active", value: true)
            .execute()
            .value

        let pathIds = pathsResponse.map { $0.id.uuidString }

        guard !pathIds.isEmpty else {
            print("⚠️ No learning paths found for language '\(language)'")
            return []
        }

        // Get lessons for these paths using the actual database schema
        let baseQuery = client
            .from("lessons")
            .select("*, content_metadata")
            .in("path_id", values: pathIds)
            .eq("is_active", value: true)

        // Filter by level if specified
        let finalQuery: PostgrestFilterBuilder
        if let level = level, level != "All" {
            let difficultyLevel = getDifficultyNumber(for: level)
            finalQuery = baseQuery.eq("difficulty_level", value: difficultyLevel)
        } else {
            finalQuery = baseQuery
        }

        let response: [SupabaseLesson] = try await finalQuery
            .order("sequence_order", ascending: true)
            .execute().value

        // Get the language name for the response
        let languageName = languageResponse.first?.name ?? language.capitalized

        print("✅ Loaded \(response.count) lessons for \(language) (\(languageName))" + (level != nil ? " at level \(level!)" : ""))
        return response
    }

    private func getDifficultyNumber(for level: String) -> Int {
        switch level {
        case "A1": return 1
        case "A2": return 2
        case "B1": return 3
        case "B2": return 4
        case "C1": return 5
        case "C2": return 6
        default: return 1
        }
    }

    private func getLanguageCode(for language: String) -> String {
        // Convert from app language names to database language codes
        switch language.lowercased() {
        case "english": return "en"
        case "spanish": return "es"
        case "french": return "fr"
        case "german": return "de"
        case "italian": return "it"
        case "portuguese": return "pt"
        case "japanese": return "ja"
        case "korean": return "ko"
        case "chinese": return "zh"
        case "arabic": return "ar"
        case "hindi": return "hi"
        case "tamil": return "ta"
        case "telugu": return "te"
        case "vietnamese": return "vi"
        case "indonesian": return "id"
        default: return language.lowercased() // fallback to original
        }
    }

    func getLesson(id: String) async throws -> SupabaseLesson? {
        let response: [SupabaseLesson] = try await client
            .from("lessons")
            .select()
            .eq("id", value: id)
            .eq("is_active", value: true)
            .execute()
            .value

        return response.first
    }

    // MARK: - Progress Tracking

    func updateProgress(
        lessonId: String,
        status: String,
        score: Int,
        timeSpent: Int
    ) async throws {
        guard let userId = currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        let progressData: [String: String?] = [
            "user_id": userId.uuidString,
            "lesson_id": lessonId,
            "status": status,
            "score": String(score),
            "time_spent_seconds": String(timeSpent),
            "completed_at": status == "completed" ? ISO8601DateFormatter().string(from: Date()) : nil
        ]

        _ = try await client
            .from("user_progress")
            .upsert(progressData)
            .execute()
    }

    func getUserProgress() async throws -> [SupabaseUserProgress] {
        guard let userId = currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        let response: [SupabaseUserProgress] = try await client
            .from("user_progress")
            .select("*, lessons(*)")
            .eq("user_id", value: userId.uuidString)
            .execute()
            .value

        return response
    }

    // MARK: - User Profile

    func updateUserProfile(
        firstName: String? = nil,
        lastName: String? = nil,
        preferredLanguages: [String]? = nil
    ) async throws {
        guard let userId = currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        var updateData: [String: String] = [:]
        if let firstName = firstName { updateData["first_name"] = firstName }
        if let lastName = lastName { updateData["last_name"] = lastName }
        if let languages = preferredLanguages {
            updateData["preferred_languages"] = languages.joined(separator: ",")
        }
        updateData["last_active_date"] = ISO8601DateFormatter().string(from: Date())

        _ = try await client
            .from("users")
            .update(updateData)
            .eq("id", value: userId.uuidString)
            .execute()
    }

    func getUserProfile() async throws -> SupabaseUserProfile? {
        guard let userId = currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }

        let response: [SupabaseUserProfile] = try await client
            .from("users")
            .select()
            .eq("id", value: userId.uuidString)
            .execute()
            .value

        return response.first
    }

    // MARK: - Simulation Methods
    
    func getSimulationPersonas() async throws -> [SupabaseSimulationPersona] {
        let response: [SupabaseSimulationPersona] = try await client
            .from("simulation_personas")
            .select("*")
            .eq("is_active", value: true)
            .order("sort_order", ascending: true)
            .execute()
            .value
        
        print("✅ Loaded \(response.count) simulation personas")
        return response
    }
    
    func getSimulations(personaId: UUID? = nil, languageId: UUID? = nil) async throws -> [SupabaseSimulation] {
        var query = client
            .from("simulations")
            .select("*")
            .eq("is_active", value: true)
        
        if let personaId = personaId {
            query = query.eq("persona_id", value: personaId.uuidString)
        }
        
        if let languageId = languageId {
            query = query.eq("language_id", value: languageId.uuidString)
        }
        
        let response: [SupabaseSimulation] = try await query
            .order("created_at", ascending: false)
            .execute()
            .value
        
        print("✅ Loaded \(response.count) simulations")
        return response
    }

    // MARK: - Private Methods

    private func setupAuthListener() {
        Task {
            await observeAuthStateChanges()
        }
    }

    private func observeAuthStateChanges() async {
        for await state in client.auth.authStateChanges {
            let session = state.session
            let user = session?.user
            updateAuthState(session: session, user: user)
        }
    }

    @MainActor
    private func updateAuthState(session: Session?, user: Auth.User?) {
        self.session = session
        self.currentUser = user
    }

    private func checkConnection() {
        Task { [weak self] in
            guard let self = self else { return }

            // Skip connection check if using placeholder values
            if self.configuredURL.contains("placeholder") {
                await MainActor.run {
                    self.isConnected = false
                    print("📱 Running in development mode - Supabase features disabled")
                }
                return
            }

            // Try multiple connection checks in order of preference
            var connectionSuccessful = false

            // First, try a simple auth check (always available)
            do {
                _ = try await self.client.auth.session
                connectionSuccessful = true
                print("✅ Supabase connection established (auth endpoint)")
            } catch {
                print("⚠️ Auth endpoint check failed: \(error.localizedDescription)")
            }

            // If auth works, try to check if database is accessible
            if connectionSuccessful {
                do {
                    // Try to access a system table that should always exist
                    _ = try await self.client
                        .rpc("version")
                        .execute()
                    print("✅ Supabase database accessible")
                } catch {
                    print("⚠️ Database access limited: \(error.localizedDescription)")
                    print("💡 This is normal if your database schema isn't set up yet")
                }
            }

            await MainActor.run {
                self.isConnected = connectionSuccessful
                if connectionSuccessful {
                    print("🌐 Supabase fully connected and ready")
                } else {
                    print("❌ Supabase connection failed")
                    print("🔧 Check your Supabase URL and API key configuration")
                }
            }
        }
    }
}

// MARK: - Error Types

enum SupabaseError: Error, Equatable {
    case notAuthenticated
    case invalidResponse
    case networkError(Error)

    var localizedDescription: String {
        switch self {
        case .notAuthenticated:
            return "User not authenticated"
        case .invalidResponse:
            return "Invalid response from server"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }

    static func == (lhs: SupabaseError, rhs: SupabaseError) -> Bool {
        switch (lhs, rhs) {
        case (.notAuthenticated, .notAuthenticated):
            return true
        case (.invalidResponse, .invalidResponse):
            return true
        case (.networkError, .networkError):
            return true
        default:
            return false
        }
    }
}

// MARK: - Helper Types

struct SupabaseAnyCodable: Codable {
    let value: Any

    init(_ value: Any) {
        self.value = value
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()

        if let intValue = try? container.decode(Int.self) {
            value = intValue
        } else if let doubleValue = try? container.decode(Double.self) {
            value = doubleValue
        } else if let stringValue = try? container.decode(String.self) {
            value = stringValue
        } else if let boolValue = try? container.decode(Bool.self) {
            value = boolValue
        } else {
            value = NSNull()
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()

        if let intValue = value as? Int {
            try container.encode(intValue)
        } else if let doubleValue = value as? Double {
            try container.encode(doubleValue)
        } else if let stringValue = value as? String {
            try container.encode(stringValue)
        } else if let boolValue = value as? Bool {
            try container.encode(boolValue)
        } else {
            try container.encodeNil()
        }
    }
}