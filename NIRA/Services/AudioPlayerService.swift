import Foundation
import AVFoundation
import SwiftUI

@MainActor
class AudioPlayerService: NSObject, ObservableObject {
    static let shared = AudioPlayerService()

    @Published var isPlaying = false
    @Published var currentAudioURL: String?
    @Published var playbackError: String?
    @Published var isLoading = false

    private var audioPlayer: AVAudioPlayer?
    private var remotePlayer: AVPlayer?
    private var audioSession: AVAudioSession
    private var downloadTask: URLSessionDataTask?

    // Cache for downloaded audio files
    private let cacheDirectory: URL
    private let fileManager = FileManager.default
    
    private override init() {
        self.audioSession = AVAudioSession.sharedInstance()

        // Setup cache directory
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        self.cacheDirectory = documentsPath.appendingPathComponent("AudioCache")

        super.init()
        setupAudioSession()
        createCacheDirectory()
    }
    
    private func setupAudioSession() {
        do {
            try audioSession.setCategory(.playback, mode: .default, options: [.allowAirPlay, .allowBluetooth])
            try audioSession.setActive(true)
        } catch {
            print("❌ Failed to setup audio session: \(error)")
            playbackError = "Audio setup failed"
        }
    }

    private func createCacheDirectory() {
        if !fileManager.fileExists(atPath: cacheDirectory.path) {
            do {
                try fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
                print("✅ Created audio cache directory: \(cacheDirectory.path)")
            } catch {
                print("❌ Failed to create cache directory: \(error)")
            }
        }
    }
    
    func playAudio(from urlString: String) {
        // Stop any currently playing audio
        stopAudio()

        // Reset error state
        playbackError = nil

        // Check if it's a remote URL (Supabase Storage or other remote URLs)
        if urlString.hasPrefix("http://") || urlString.hasPrefix("https://") {
            Task {
                await playRemoteAudio(from: urlString)
            }
        } else {
            // Check if file exists locally
            if let localURL = getLocalAudioURL(from: urlString) {
                playLocalAudio(url: localURL, originalURLString: urlString)
            } else {
                playbackError = "Audio file not found"
                print("🔊 Audio file not found: \(urlString)")
            }
        }
    }
    
    private func getLocalAudioURL(from urlString: String) -> URL? {
        // Extract filename from the full path
        let filename = URL(fileURLWithPath: urlString).lastPathComponent
        print("🔍 Looking for audio file: \(filename)")

        // Check in app bundle first
        if let bundleURL = Bundle.main.url(forResource: filename.replacingOccurrences(of: ".mp3", with: ""), withExtension: "mp3") {
            print("✅ Found in app bundle: \(bundleURL.path)")
            return bundleURL
        }

        // Check in the actual project Assets directory
        let projectAssetsPath = URL(fileURLWithPath: "/Users/<USER>/Documents/NIRA/Assets/Audio").appendingPathComponent(filename)
        if FileManager.default.fileExists(atPath: projectAssetsPath.path) {
            print("✅ Found in project assets: \(projectAssetsPath.path)")
            return projectAssetsPath
        }

        // Check in documents directory
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let audioPath = documentsPath.appendingPathComponent("Audio").appendingPathComponent(filename)

        if FileManager.default.fileExists(atPath: audioPath.path) {
            print("✅ Found in documents: \(audioPath.path)")
            return audioPath
        }

        // Check the original path if it exists
        let originalURL = URL(fileURLWithPath: urlString)
        if FileManager.default.fileExists(atPath: originalURL.path) {
            print("✅ Found at original path: \(originalURL.path)")
            return originalURL
        }

        print("❌ Audio file not found anywhere: \(filename)")
        print("   Checked paths:")
        print("   - App bundle")
        print("   - \(projectAssetsPath.path)")
        print("   - \(audioPath.path)")
        print("   - \(originalURL.path)")
        return nil
    }
    
    private func playRemoteAudio(from urlString: String) async {
        guard let url = URL(string: urlString) else {
            await MainActor.run {
                playbackError = "Invalid audio URL"
                print("❌ Invalid URL: \(urlString)")
            }
            return
        }

        print("🌐 Playing remote audio: \(url.lastPathComponent)")

        await MainActor.run {
            isLoading = true
        }

        // Check if we have a cached version first
        let cachedURL = getCachedAudioURL(for: url)
        if fileManager.fileExists(atPath: cachedURL.path) {
            print("✅ Found cached audio: \(cachedURL.lastPathComponent)")
            await MainActor.run {
                isLoading = false
                playLocalAudio(url: cachedURL, originalURLString: urlString)
            }
            return
        }

        // Download and cache the audio file
        do {
            let (data, response) = try await URLSession.shared.data(from: url)

            guard let httpResponse = response as? HTTPURLResponse,
                  httpResponse.statusCode == 200 else {
                await MainActor.run {
                    isLoading = false
                    playbackError = "Failed to download audio file"
                    print("❌ HTTP error downloading audio")
                }
                return
            }

            // Save to cache
            try data.write(to: cachedURL)
            print("✅ Cached audio file: \(cachedURL.lastPathComponent)")

            // Play the cached file
            await MainActor.run {
                isLoading = false
                playLocalAudio(url: cachedURL, originalURLString: urlString)
            }

        } catch {
            await MainActor.run {
                isLoading = false
                playbackError = "Failed to download audio: \(error.localizedDescription)"
                print("❌ Remote audio download error: \(error)")
            }
        }
    }

    private func playLocalAudio(url: URL, originalURLString: String) {
        do {
            audioPlayer = try AVAudioPlayer(contentsOf: url)
            audioPlayer?.delegate = self
            audioPlayer?.prepareToPlay()

            if audioPlayer?.play() == true {
                isPlaying = true
                currentAudioURL = originalURLString
                playbackError = nil
                print("🔊 Playing local audio: \(url.lastPathComponent)")
            } else {
                playbackError = "Failed to start playback"
                print("❌ Failed to start audio playback")
            }
        } catch {
            playbackError = "Audio playback error: \(error.localizedDescription)"
            print("❌ Audio playback error: \(error)")
        }
    }

    private func getCachedAudioURL(for remoteURL: URL) -> URL {
        let filename = remoteURL.lastPathComponent
        return cacheDirectory.appendingPathComponent(filename)
    }

    func stopAudio() {
        // Cancel any ongoing download
        downloadTask?.cancel()
        downloadTask = nil

        // Stop audio players
        audioPlayer?.stop()
        audioPlayer = nil
        remotePlayer?.pause()
        remotePlayer = nil

        // Reset state
        isPlaying = false
        isLoading = false
        currentAudioURL = nil
        playbackError = nil
    }
    
    func pauseAudio() {
        audioPlayer?.pause()
        remotePlayer?.pause()
        isPlaying = false
    }

    // MARK: - Cache Management

    func clearAudioCache() {
        do {
            let cacheContents = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
            for fileURL in cacheContents {
                try fileManager.removeItem(at: fileURL)
            }
            print("✅ Cleared audio cache")
        } catch {
            print("❌ Failed to clear audio cache: \(error)")
        }
    }

    func getCacheSize() -> Int64 {
        do {
            let cacheContents = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey])
            var totalSize: Int64 = 0
            for fileURL in cacheContents {
                let resourceValues = try fileURL.resourceValues(forKeys: [.fileSizeKey])
                totalSize += Int64(resourceValues.fileSize ?? 0)
            }
            return totalSize
        } catch {
            print("❌ Failed to calculate cache size: \(error)")
            return 0
        }
    }
    
    func resumeAudio() {
        if audioPlayer?.play() == true {
            isPlaying = true
        }
    }
    
    // MARK: - Test Audio Generation
    
    func generateTestAudio(text: String, filename: String) {
        // This is a placeholder for future ElevenLabs integration
        print("🎵 Would generate audio for: '\(text)' -> \(filename)")
        
        // For now, create a placeholder file
        createPlaceholderAudio(filename: filename)
    }
    
    private func createPlaceholderAudio(filename: String) {
        // Create a simple beep sound as placeholder
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let audioDir = documentsPath.appendingPathComponent("Audio")
        
        // Create audio directory if it doesn't exist
        try? FileManager.default.createDirectory(at: audioDir, withIntermediateDirectories: true)
        
        let audioPath = audioDir.appendingPathComponent(filename)
        
        // Create a simple audio file (placeholder)
        let audioData = Data() // Empty data for now
        try? audioData.write(to: audioPath)
        
        print("📁 Created placeholder audio file: \(audioPath.path)")
    }
}

// MARK: - AVAudioPlayerDelegate

extension AudioPlayerService: AVAudioPlayerDelegate {
    nonisolated func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        Task { @MainActor in
            isPlaying = false
            currentAudioURL = nil
            if !flag {
                playbackError = "Playback finished with error"
            }
        }
    }
    
    nonisolated func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        Task { @MainActor in
            isPlaying = false
            currentAudioURL = nil
            playbackError = "Audio decode error: \(error?.localizedDescription ?? "Unknown error")"
        }
    }
}

// MARK: - Audio Button Component

struct AudioButton: View {
    let audioURL: String?
    let size: CGFloat
    let color: Color
    @StateObject private var audioService = AudioPlayerService.shared
    
    init(audioURL: String?, size: CGFloat = 32, color: Color = .blue) {
        self.audioURL = audioURL
        self.size = size
        self.color = color
    }
    
    var body: some View {
        Button(action: {
            guard let audioURL = audioURL else {
                print("⚠️ No audio URL provided")
                return
            }

            if audioService.isPlaying && audioService.currentAudioURL == audioURL {
                audioService.stopAudio()
            } else {
                audioService.playAudio(from: audioURL)
            }
        }) {
            Group {
                if isCurrentlyLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                        .progressViewStyle(CircularProgressViewStyle(tint: color))
                } else {
                    Image(systemName: isCurrentlyPlaying ? "stop.fill" : "speaker.wave.2.fill")
                        .font(.system(size: size * 0.6, weight: .medium))
                        .foregroundColor(audioURL != nil ? color : .gray)
                }
            }
            .frame(width: size, height: size)
            .background(
                Circle()
                    .fill(audioURL != nil ? color.opacity(0.1) : Color.gray.opacity(0.1))
            )
            .scaleEffect(isCurrentlyPlaying ? 1.1 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isCurrentlyPlaying)
        }
        .disabled(audioURL == nil || isCurrentlyLoading)
    }
    
    private var isCurrentlyPlaying: Bool {
        audioService.isPlaying && audioService.currentAudioURL == audioURL
    }

    private var isCurrentlyLoading: Bool {
        audioService.isLoading && audioService.currentAudioURL == audioURL
    }
}
