#!/usr/bin/env python3
"""
Generate All Tamil A1 Lessons with Audio
Complete the Tamil curriculum by generating all 14 remaining lessons with ElevenLabs audio
"""

import os
import json
import time
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client, Client
from elevenlabs.client import ElevenLabs
import openai

# Load environment variables
load_dotenv()

# Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_ANON_KEY = os.getenv("SUPABASE_ANON_KEY")
ELEVENLABS_API_KEY = os.getenv("ELEVENLABS_API_KEY")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

# Initialize clients
supabase: Client = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)
elevenlabs_client = ElevenLabs(api_key=ELEVENLABS_API_KEY)
openai.api_key = OPENAI_API_KEY

# Tamil voice configuration
TAMIL_VOICE_ID = "9BWtsMINqrJLrRacOk9x"

# Tamil A1 Lesson Topics (excluding lesson 1 which already exists)
TAMIL_LESSONS = [
    {
        "title": "Family Members and Relationships",
        "description": "Learn to talk about family members, relationships, and basic personal information",
        "topic": "Family and Relationships",
        "sequence": 2,
        "vocabulary": [
            {"word": "அம்மா", "translation": "Mother", "example": "என் அம்மா நல்லவர்"},
            {"word": "அப்பா", "translation": "Father", "example": "என் அப்பா வேலைக்கு போகிறார்"},
            {"word": "அண்ணன்", "translation": "Elder brother", "example": "என் அண்ணன் படிக்கிறான்"},
            {"word": "தங்கை", "translation": "Younger sister", "example": "என் தங்கை சிறியவள்"},
            {"word": "குடும்பம்", "translation": "Family", "example": "எங்கள் குடும்பம் பெரியது"}
        ]
    },
    {
        "title": "Numbers and Counting",
        "description": "Master Tamil numbers from 1-100, basic counting, and simple arithmetic expressions",
        "topic": "Numbers and Mathematics",
        "sequence": 3,
        "vocabulary": [
            {"word": "ஒன்று", "translation": "One", "example": "ஒன்று ஆப்பிள்"},
            {"word": "இரண்டு", "translation": "Two", "example": "இரண்டு பேனா"},
            {"word": "மூன்று", "translation": "Three", "example": "மூன்று புத்தகம்"},
            {"word": "நான்கு", "translation": "Four", "example": "நான்கு நாற்காலி"},
            {"word": "ஐந்து", "translation": "Five", "example": "ஐந்து விரல்"}
        ]
    },
    {
        "title": "Days, Months, and Time",
        "description": "Learn days of the week, months, telling time, and basic temporal expressions",
        "topic": "Time and Calendar",
        "sequence": 4,
        "vocabulary": [
            {"word": "திங்கள்", "translation": "Monday", "example": "திங்கள் கிழமை வேலை"},
            {"word": "செவ்வாய்", "translation": "Tuesday", "example": "செவ்வாய் கிழமை பள்ளி"},
            {"word": "புதன்", "translation": "Wednesday", "example": "புதன் கிழமை விடுமுறை"},
            {"word": "வியாழன்", "translation": "Thursday", "example": "வியாழன் கிழமை சந்திப்பு"},
            {"word": "வெள்ளி", "translation": "Friday", "example": "வெள்ளி கிழமை திருவிழா"}
        ]
    },
    {
        "title": "Colors and Descriptions",
        "description": "Describe colors, basic adjectives, and simple physical descriptions",
        "topic": "Colors and Adjectives",
        "sequence": 5,
        "vocabulary": [
            {"word": "சிவப்பு", "translation": "Red", "example": "சிவப்பு ரோஜா"},
            {"word": "நீலம்", "translation": "Blue", "example": "நீலம் வானம்"},
            {"word": "பச்சை", "translation": "Green", "example": "பச்சை இலை"},
            {"word": "மஞ்சள்", "translation": "Yellow", "example": "மஞ்சள் பூ"},
            {"word": "வெள்ளை", "translation": "White", "example": "வெள்ளை பால்"}
        ]
    },
    {
        "title": "Food and Dining",
        "description": "Essential food vocabulary, dining etiquette, and restaurant conversations",
        "topic": "Food and Dining",
        "sequence": 6,
        "vocabulary": [
            {"word": "சாதம்", "translation": "Rice", "example": "சாதம் சுவையாக இருக்கிறது"},
            {"word": "சாம்பார்", "translation": "Sambar", "example": "சாம்பார் காரமாக இருக்கிறது"},
            {"word": "இட்லி", "translation": "Idli", "example": "இட்லி மென்மையாக இருக்கிறது"},
            {"word": "தோசை", "translation": "Dosa", "example": "தோசை பொறிப்பாக இருக்கிறது"},
            {"word": "தண்ணீர்", "translation": "Water", "example": "தண்ணீர் குளிர்ச்சியாக இருக்கிறது"}
        ]
    }
]

class TamilLessonGenerator:
    def __init__(self):
        self.generated_count = 0
        self.failed_count = 0
        
    def generate_audio(self, text, filename):
        """Generate audio using ElevenLabs"""
        try:
            print(f"🎵 Generating audio for: {text[:30]}...")

            audio = elevenlabs_client.text_to_speech.convert(
                voice_id=TAMIL_VOICE_ID,
                text=text,
                model_id="eleven_multilingual_v2",
                voice_settings={
                    "stability": 0.5,
                    "similarity_boost": 0.75,
                    "style": 0.0,
                    "use_speaker_boost": True
                }
            )
            
            # Save audio file
            audio_path = f"/tmp/{filename}"
            with open(audio_path, "wb") as f:
                for chunk in audio:
                    f.write(chunk)
            
            print(f"✅ Audio generated: {filename}")
            return audio_path
            
        except Exception as e:
            print(f"❌ Audio generation failed: {e}")
            return None
    
    def create_lesson_content(self, lesson_config):
        """Create comprehensive lesson content"""
        
        # Generate vocabulary with audio
        vocabulary_with_audio = []
        for i, vocab in enumerate(lesson_config["vocabulary"]):
            word_audio = self.generate_audio(
                vocab["word"], 
                f"lesson_tamil_a1_lesson_{lesson_config['sequence']}_vocab_{i+1}_word.mp3"
            )
            example_audio = self.generate_audio(
                vocab["example"], 
                f"lesson_tamil_a1_lesson_{lesson_config['sequence']}_vocab_{i+1}_example.mp3"
            )
            
            vocabulary_with_audio.append({
                "word": vocab["word"],
                "translation": vocab["translation"],
                "example": vocab["example"],
                "word_audio_url": word_audio,
                "example_audio_url": example_audio
            })
        
        # Create lesson structure matching database schema
        lesson_content = {
            "path_id": "6b427613-420f-4586-bce8-2773d722f0b4",  # Tamil A1 learning path ID
            "title": lesson_config["title"],
            "description": lesson_config["description"],
            "lesson_type": "vocabulary",
            "difficulty_level": 1,
            "estimated_duration": 25,
            "sequence_order": lesson_config["sequence"],
            "learning_objectives": [
                f"Learn {lesson_config['topic'].lower()} vocabulary",
                "Practice pronunciation with audio",
                "Understand basic sentence structure"
            ],
            "vocabulary_focus": [],
            "grammar_concepts": ["Basic sentence structure", "Noun usage"],
            "cultural_notes": f"Tamil {lesson_config['topic'].lower()} in daily life context",
            "prerequisite_lessons": [],
            "content_metadata": {
                "vocabulary": vocabulary_with_audio,
                "conversations": [
                    {
                        "title": f"Practice {lesson_config['topic']}",
                        "exchanges": [
                            {
                                "speaker": "Teacher",
                                "text": f"{lesson_config['vocabulary'][0]['word']} என்றால் என்ன?",
                                "translation": f"What does {lesson_config['vocabulary'][0]['word']} mean?"
                            },
                            {
                                "speaker": "Student",
                                "text": f"{lesson_config['vocabulary'][0]['word']} என்றால் {lesson_config['vocabulary'][0]['translation']}",
                                "translation": f"{lesson_config['vocabulary'][0]['word']} means {lesson_config['vocabulary'][0]['translation']}"
                            }
                        ]
                    }
                ],
                "exercises": [
                    {
                        "type": "multiple_choice",
                        "question": f"'{lesson_config['vocabulary'][0]['translation']}' என்பதற்கு தமிழில் என்ன?",
                        "options": [vocab["word"] for vocab in lesson_config["vocabulary"]],
                        "correct_answer": lesson_config['vocabulary'][0]['word']
                    }
                ]
            },
            "is_active": True,
            "has_audio": True,
            "audio_metadata": {
                "total_files": len(lesson_config["vocabulary"]) * 2,
                "voice_id": TAMIL_VOICE_ID,
                "generated_at": datetime.now().isoformat()
            }
        }
        
        return lesson_content
    
    def upload_to_supabase(self, lesson_content):
        """Upload lesson to Supabase"""
        try:
            print(f"📤 Uploading lesson: {lesson_content['title']}")
            
            response = supabase.table("lessons").insert(lesson_content).execute()
            
            if response.data:
                print(f"✅ Lesson uploaded successfully: {lesson_content['title']}")
                return True
            else:
                print(f"❌ Upload failed: {response}")
                return False
                
        except Exception as e:
            print(f"❌ Upload error: {e}")
            return False
    
    def generate_all_lessons(self):
        """Generate all Tamil A1 lessons"""
        print("🎯 Starting Tamil A1 Curriculum Generation")
        print(f"📚 Generating {len(TAMIL_LESSONS)} lessons with audio")
        print("=" * 60)
        
        for i, lesson_config in enumerate(TAMIL_LESSONS, 1):
            print(f"\n📖 Lesson {lesson_config['sequence']}: {lesson_config['title']}")
            print("-" * 50)
            
            try:
                # Generate lesson content with audio
                lesson_content = self.create_lesson_content(lesson_config)
                
                # Upload to Supabase
                if self.upload_to_supabase(lesson_content):
                    self.generated_count += 1
                    print(f"✅ Lesson {lesson_config['sequence']} completed successfully")
                else:
                    self.failed_count += 1
                    print(f"❌ Lesson {lesson_config['sequence']} upload failed")
                
                # Brief pause between lessons
                time.sleep(2)
                
            except Exception as e:
                self.failed_count += 1
                print(f"❌ Lesson {lesson_config['sequence']} generation failed: {e}")
        
        # Final summary
        print("\n" + "=" * 60)
        print("🎉 Tamil A1 Curriculum Generation Complete!")
        print(f"✅ Successfully generated: {self.generated_count} lessons")
        print(f"❌ Failed: {self.failed_count} lessons")
        print(f"📊 Success rate: {(self.generated_count/(self.generated_count+self.failed_count)*100):.1f}%")

def main():
    """Main execution function"""
    print("🚀 NIRA Tamil A1 Curriculum Generator")
    print("Generating comprehensive lessons with ElevenLabs audio")
    print("=" * 60)
    
    # Verify API keys
    if not all([SUPABASE_URL, SUPABASE_ANON_KEY, ELEVENLABS_API_KEY]):
        print("❌ Missing required API keys. Please check your .env file.")
        return
    
    # Initialize generator
    generator = TamilLessonGenerator()
    
    # Generate all lessons
    generator.generate_all_lessons()

if __name__ == "__main__":
    main()
