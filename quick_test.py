#!/usr/bin/env python3
"""
Quick test to verify API connections
"""

import os
import json

# API Keys
GEMINI_API_KEY = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
OPENAI_API_KEY = "***********************************************************************************************************************************************************************"

def test_openai():
    """Test OpenAI API"""
    try:
        from openai import OpenAI
        client = OpenAI(api_key=OPENAI_API_KEY)
        
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": "Say hello in Tamil"}],
            max_tokens=50
        )
        
        print("✅ OpenAI API working!")
        print(f"Response: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI API failed: {e}")
        return False

def test_gemini():
    """Test Gemini API"""
    try:
        import google.generativeai as genai
        genai.configure(api_key=GEMINI_API_KEY)
        
        model = genai.GenerativeModel('gemini-2.0-flash-exp')
        response = model.generate_content("Say hello in Tamil")
        
        print("✅ Gemini API working!")
        print(f"Response: {response.text}")
        return True
        
    except Exception as e:
        print(f"❌ Gemini API failed: {e}")
        return False

def main():
    print("🧪 Quick API Test")
    print("=" * 30)
    
    openai_ok = test_openai()
    gemini_ok = test_gemini()
    
    if openai_ok or gemini_ok:
        print("\n✅ At least one AI API is working!")
        return True
    else:
        print("\n❌ Both AI APIs failed!")
        return False

if __name__ == "__main__":
    main()
