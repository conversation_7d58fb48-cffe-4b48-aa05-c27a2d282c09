#!/usr/bin/env python3
"""
Test script to generate comprehensive content for a single lesson
"""

import json
import time
from comprehensive_lesson_generator import ComprehensiveLessonGenerator

def test_single_lesson():
    """Test comprehensive content generation for one lesson"""
    
    print("🧪 TESTING COMPREHENSIVE LESSON GENERATION")
    print("=" * 50)
    
    generator = ComprehensiveLessonGenerator()
    
    # Test with Lesson 1: Basic Greetings and Introductions
    topic = "Basic Greetings and Introductions"
    lesson_number = 1
    
    print(f"📖 Testing Lesson {lesson_number}: {topic}")
    print("-" * 40)
    
    try:
        # Generate comprehensive content
        lesson_data = generator.generate_comprehensive_lesson_content(topic, lesson_number)
        
        if lesson_data:
            # Count content
            vocab_count = len(lesson_data.get('vocabulary', []))
            conv_count = len(lesson_data.get('conversations', []))
            grammar_count = len(lesson_data.get('grammar_points', []))
            exercise_count = len(lesson_data.get('exercises', []))
            
            print(f"\n📊 CONTENT GENERATED:")
            print(f"   • Vocabulary Items: {vocab_count}/25")
            print(f"   • Conversations: {conv_count}/15")
            print(f"   • Grammar Points: {grammar_count}/10")
            print(f"   • Exercises: {exercise_count}/5")
            
            # Check if targets met
            targets_met = (vocab_count >= 20 and conv_count >= 12 and 
                          grammar_count >= 8 and exercise_count >= 4)
            
            if targets_met:
                print("✅ Content targets met!")
            else:
                print("⚠️ Some content targets not fully met")
            
            # Save test lesson
            with open('test_lesson_comprehensive.json', 'w', encoding='utf-8') as f:
                json.dump(lesson_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 Test lesson saved to: test_lesson_comprehensive.json")
            
            # Show sample content
            print(f"\n📝 SAMPLE CONTENT:")
            
            if lesson_data.get('vocabulary'):
                vocab = lesson_data['vocabulary'][0]
                print(f"   Vocabulary: {vocab.get('word', 'N/A')} = {vocab.get('translation', 'N/A')}")
                print(f"   Example: {vocab.get('example', 'N/A')}")
            
            if lesson_data.get('conversations'):
                conv = lesson_data['conversations'][0]
                print(f"   Conversation: {conv.get('title', 'N/A')}")
                if conv.get('exchanges'):
                    exchange = conv['exchanges'][0]
                    print(f"   Exchange: {exchange.get('text', 'N/A')}")
            
            if lesson_data.get('grammar_points'):
                grammar = lesson_data['grammar_points'][0]
                print(f"   Grammar: {grammar.get('rule', 'N/A')}")
            
            if lesson_data.get('exercises'):
                exercise = lesson_data['exercises'][0]
                print(f"   Exercise: {exercise.get('type', 'N/A')} - {exercise.get('question', 'N/A')}")
            
            # Test audio generation for a few items (not all to save API calls)
            print(f"\n🎵 TESTING AUDIO GENERATION (Sample):")
            
            # Test 2 vocabulary audio files
            audio_count = 0
            for i, vocab in enumerate(lesson_data.get('vocabulary', [])[:2], 1):
                if 'word' in vocab:
                    filename = f"test_lesson_1_vocab_{i}_word.mp3"
                    if generator.generate_elevenlabs_audio(vocab['word'], filename):
                        audio_count += 1
                        print(f"   ✅ Generated audio for: {vocab['word']}")
                    time.sleep(1)
            
            # Test 1 conversation audio
            if lesson_data.get('conversations') and lesson_data['conversations'][0].get('exchanges'):
                exchange = lesson_data['conversations'][0]['exchanges'][0]
                if 'text' in exchange:
                    filename = f"test_lesson_1_conv_1_1.mp3"
                    if generator.generate_elevenlabs_audio(exchange['text'], filename):
                        audio_count += 1
                        print(f"   ✅ Generated audio for conversation")
            
            print(f"\n🎯 AUDIO TEST RESULTS:")
            print(f"   • Audio files generated: {audio_count}/3")
            print(f"   • Audio generation: {'✅ Working' if audio_count > 0 else '❌ Failed'}")
            
            return True
            
        else:
            print("❌ Failed to generate lesson content")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def main():
    """Main test function"""
    
    success = test_single_lesson()
    
    print(f"\n" + "=" * 50)
    if success:
        print("🎉 SINGLE LESSON TEST SUCCESSFUL!")
        print("✅ Ready to process all 25 lessons")
        print("\nTo run full generation:")
        print("python3 comprehensive_lesson_generator.py")
    else:
        print("❌ SINGLE LESSON TEST FAILED!")
        print("⚠️ Fix issues before running full generation")
    print("=" * 50)

if __name__ == "__main__":
    main()
