#!/usr/bin/env python3
"""
Test the final comprehensive generator with a single lesson
"""

import json
import os
from final_comprehensive_generator import generate_comprehensive_lesson, generate_audio_for_lesson

def test_single_lesson():
    """Test comprehensive generation for one lesson"""
    
    print("🧪 TESTING FINAL COMPREHENSIVE GENERATOR")
    print("=" * 50)
    print("📚 Testing single lesson generation")
    print("🎯 Target: 25 vocab + 15 conversations + 10 grammar + 5 exercises")
    print("🎵 Audio: Arnold voice (limited)")
    print("=" * 50)
    
    # Create directories
    os.makedirs('generated_audio', exist_ok=True)
    os.makedirs('lesson_content', exist_ok=True)
    
    # Test with first lesson
    topic = "Basic Greetings and Introductions"
    lesson_number = 1
    
    print(f"\n📖 Testing Lesson {lesson_number}: {topic}")
    print("-" * 40)
    
    try:
        # Generate comprehensive content
        lesson_data = generate_comprehensive_lesson(topic, lesson_number)
        
        if lesson_data:
            # Count content
            vocab_count = len(lesson_data.get('vocabulary', []))
            conv_count = len(lesson_data.get('conversations', []))
            grammar_count = len(lesson_data.get('grammar_points', []))
            exercise_count = len(lesson_data.get('exercises', []))
            
            print(f"\n📊 CONTENT GENERATED:")
            print(f"   • Vocabulary: {vocab_count}/25 {'✅' if vocab_count >= 20 else '⚠️'}")
            print(f"   • Conversations: {conv_count}/15 {'✅' if conv_count >= 12 else '⚠️'}")
            print(f"   • Grammar: {grammar_count}/10 {'✅' if grammar_count >= 8 else '⚠️'}")
            print(f"   • Exercises: {exercise_count}/5 {'✅' if exercise_count >= 4 else '⚠️'}")
            
            # Generate limited audio
            print(f"\n🎵 GENERATING SAMPLE AUDIO:")
            audio_count = generate_audio_for_lesson(lesson_data, max_audio_files=10)
            print(f"   • Audio files generated: {audio_count}")
            
            # Save lesson data
            lesson_file = f'lesson_content/test_lesson_{lesson_number}_{topic.replace(" ", "_").lower()}.json'
            with open(lesson_file, 'w', encoding='utf-8') as f:
                json.dump(lesson_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 SAVED:")
            print(f"   • Lesson file: {lesson_file}")
            print(f"   • Audio files: generated_audio/")
            
            # Show sample content
            print(f"\n📝 SAMPLE CONTENT:")
            if lesson_data.get('vocabulary'):
                vocab = lesson_data['vocabulary'][0]
                print(f"   Vocab: {vocab.get('word')} = {vocab.get('translation')}")
                print(f"   Example: {vocab.get('example')}")
            
            if lesson_data.get('conversations'):
                conv = lesson_data['conversations'][0]
                print(f"   Conversation: {conv.get('title')}")
                if conv.get('exchanges'):
                    exchange = conv['exchanges'][0]
                    print(f"   Exchange: {exchange.get('text')} = {exchange.get('translation')}")
            
            if lesson_data.get('grammar_points'):
                grammar = lesson_data['grammar_points'][0]
                print(f"   Grammar: {grammar.get('rule')}")
                print(f"   Explanation: {grammar.get('explanation')}")
            
            if lesson_data.get('exercises'):
                exercise = lesson_data['exercises'][0]
                print(f"   Exercise: {exercise.get('type')} - {exercise.get('question')}")
            
            # Check success criteria
            success = (vocab_count >= 20 and conv_count >= 12 and 
                      grammar_count >= 8 and exercise_count >= 4)
            
            print(f"\n🎯 TEST RESULT:")
            if success:
                print("✅ SINGLE LESSON TEST SUCCESSFUL!")
                print("🚀 Ready to run full 25-lesson generation")
                print("\nTo run full generation:")
                print("python3 final_comprehensive_generator.py")
                return True
            else:
                print("⚠️ SINGLE LESSON TEST PARTIALLY SUCCESSFUL")
                print("🔧 Some content targets not fully met")
                return False
            
        else:
            print("❌ Failed to generate lesson content")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def main():
    """Main test function"""
    
    success = test_single_lesson()
    
    print(f"\n" + "=" * 50)
    if success:
        print("🎉 SINGLE LESSON TEST PASSED")
        print("✅ System ready for full 25-lesson generation")
        print("\nNext steps:")
        print("1. Run: python3 final_comprehensive_generator.py")
        print("2. Wait ~60-90 minutes for completion")
        print("3. Check results in lesson_content/ and generated_audio/")
    else:
        print("⚠️ SINGLE LESSON TEST ISSUES")
        print("🔧 Review and fix before proceeding")
    print("=" * 50)

if __name__ == "__main__":
    main()
