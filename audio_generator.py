#!/usr/bin/env python3
"""
Audio Generator - Generate audio files for all lesson content using ElevenLabs
"""

import json
import os
import requests
import time
from typing import Dict, List, Any
import urllib.parse

# Configuration
ELEVENLABS_API_KEY = os.getenv('ELEVENLABS_API_KEY', 'your_elevenlabs_key_here')
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = os.getenv('SUPABASE_KEY', 'your_supabase_key_here')

# Voice configurations (User selected voices)
VOICE_CONFIGS = {
    "arnold_male": {
        "voice_id": "VR6AewLTigWG4xSOukaG",
        "name": "Arnold Clear Male",
        "settings": {"stability": 0.8, "similarity_boost": 0.9, "style": 0.1, "use_speaker_boost": False}
    },
    "sam_male": {
        "voice_id": "yoZ06aMxZJJ28mfd3POQ", 
        "name": "Sam Natural Male",
        "settings": {"stability": 0.6, "similarity_boost": 0.8, "style": 0.3, "use_speaker_boost": True}
    },
    "elli_female": {
        "voice_id": "MF3mGyEYCl7XYWbV9V6O",
        "name": "Elli Expressive Female", 
        "settings": {"stability": 0.4, "similarity_boost": 0.7, "style": 0.6, "use_speaker_boost": True}
    },
    "freya_female": {
        "voice_id": "jsCqWAovK2LkecY7zXl4",
        "name": "Freya Gentle Female",
        "settings": {"stability": 0.7, "similarity_boost": 0.85, "style": 0.25, "use_speaker_boost": True}
    }
}

class AudioGenerator:
    def __init__(self):
        self.elevenlabs_headers = {
            'Accept': 'audio/mpeg',
            'Content-Type': 'application/json',
            'xi-api-key': ELEVENLABS_API_KEY
        }
        
        self.supabase_headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
        
    def generate_audio(self, text: str, voice_config: Dict, filename: str) -> bool:
        """Generate audio using ElevenLabs API"""
        
        url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_config['voice_id']}"
        
        data = {
            "text": text,
            "model_id": "eleven_multilingual_v2",
            "voice_settings": voice_config['settings']
        }
        
        try:
            response = requests.post(url, json=data, headers=self.elevenlabs_headers)
            
            if response.status_code == 200:
                # Save audio file locally first
                os.makedirs('generated_audio', exist_ok=True)
                local_path = f"generated_audio/{filename}"
                
                with open(local_path, 'wb') as f:
                    f.write(response.content)
                
                # Upload to Supabase Storage
                return self.upload_to_supabase_storage(local_path, filename)
            else:
                print(f"❌ ElevenLabs API error for {filename}: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Audio generation failed for {filename}: {e}")
            return False
    
    def upload_to_supabase_storage(self, local_path: str, filename: str) -> bool:
        """Upload audio file to Supabase Storage"""
        
        storage_path = f"lesson-audio/tamil/a1/{filename}"
        
        try:
            with open(local_path, 'rb') as f:
                files = {'file': (filename, f, 'audio/mpeg')}
                
                upload_url = f"{SUPABASE_URL}/storage/v1/object/lesson-audio/tamil/a1/{filename}"
                
                headers = {
                    'Authorization': f'Bearer {SUPABASE_KEY}',
                }
                
                response = requests.post(upload_url, files=files, headers=headers)
                
                if response.status_code in [200, 201]:
                    print(f"✅ Uploaded: {filename}")
                    return True
                else:
                    print(f"❌ Upload failed for {filename}: {response.text}")
                    return False
                    
        except Exception as e:
            print(f"❌ Upload error for {filename}: {e}")
            return False
    
    def generate_lesson_audio(self, lesson_data: Dict[str, Any], lesson_number: int, voice_key: str = "arnold_male"):
        """Generate all audio files for a lesson"""
        
        voice_config = VOICE_CONFIGS[voice_key]
        print(f"\n🎵 Generating audio for Lesson {lesson_number} using {voice_config['name']}")
        
        success_count = 0
        total_count = 0
        
        # Generate vocabulary audio
        for i, vocab in enumerate(lesson_data.get('vocabulary', []), 1):
            # Word audio
            word_filename = f"lesson_tamil_a1_lesson_{lesson_number}_vocab_{i}_word.mp3"
            if self.generate_audio(vocab['word'], voice_config, word_filename):
                success_count += 1
            total_count += 1
            
            # Example audio
            example_filename = f"lesson_tamil_a1_lesson_{lesson_number}_vocab_{i}_example.mp3"
            if self.generate_audio(vocab['example'], voice_config, example_filename):
                success_count += 1
            total_count += 1
            
            # Rate limiting
            time.sleep(1)
        
        # Generate conversation audio
        for conv_idx, conversation in enumerate(lesson_data.get('conversations', []), 1):
            for ex_idx, exchange in enumerate(conversation.get('exchanges', []), 1):
                conv_filename = f"lesson_tamil_a1_lesson_{lesson_number}_conv_{conv_idx}_ex_{ex_idx}.mp3"
                if self.generate_audio(exchange['text'], voice_config, conv_filename):
                    success_count += 1
                total_count += 1
                
                time.sleep(1)
        
        print(f"📊 Audio generation: {success_count}/{total_count} files successful")
        return success_count, total_count
    
    def generate_all_audio(self, lessons_file: str = 'generated_a1_lessons.json'):
        """Generate audio for all lessons"""
        
        try:
            with open(lessons_file, 'r', encoding='utf-8') as f:
                lessons = json.load(f)
            
            print("🎵 NIRA Audio Generator - Creating Audio for New Lessons")
            print("=" * 60)
            
            total_success = 0
            total_files = 0
            
            for lesson in lessons:
                lesson_number = lesson['sequence_order']
                success, total = self.generate_lesson_audio(lesson, lesson_number)
                total_success += success
                total_files += total
            
            print(f"\n🎉 Audio Generation Complete!")
            print(f"✅ Successfully generated: {total_success}/{total_files} audio files")
            print(f"📁 Files uploaded to Supabase Storage")
            
        except Exception as e:
            print(f"❌ Audio generation failed: {e}")

def main():
    """Main execution function"""
    generator = AudioGenerator()
    generator.generate_all_audio()

if __name__ == "__main__":
    main()
