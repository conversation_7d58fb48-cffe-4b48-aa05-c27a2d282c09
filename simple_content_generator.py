#!/usr/bin/env python3
"""
Simple Content Generator - Create A1 lessons using OpenAI
"""

import json
import time
from openai import OpenAI

# API Configuration
OPENAI_API_KEY = "***********************************************************************************************************************************************************************"

# Initialize OpenAI client
client = OpenAI(api_key=OPENAI_API_KEY)

# A1 Lesson Topics (14 new lessons to reach 25 total)
A1_LESSON_TOPICS = [
    "Personal Information and Identity",
    "Home and Living Spaces", 
    "Daily Routines and Activities",
    "Shopping and Money",
    "Directions and Locations",
    "Health and Body",
    "Hobbies and Interests",
    "Work and Professions",
    "Education and School",
    "Technology and Communication",
    "Emotions and Feelings",
    "Festivals and Celebrations",
    "Animals and Nature",
    "Travel and Transportation Advanced"
]

def generate_lesson_content(topic: str, lesson_number: int) -> dict:
    """Generate comprehensive lesson content using OpenAI"""
    
    prompt = f"""
    Create a comprehensive Tamil A1 beginner lesson for: "{topic}"
    
    Generate exactly:
    - 25 vocabulary items (Tamil word, English translation, Tamil example sentence)
    - 15 conversation exchanges (realistic dialogues using the vocabulary)
    - 10 grammar points (rules, explanations, examples, tips)
    - 5 practice exercises (multiple choice, fill-in-blanks, matching, translation)
    
    Return as valid JSON with this exact structure:
    {{
        "title": "{topic}",
        "vocabulary": [
            {{
                "word": "tamil_word",
                "translation": "english_translation",
                "example": "tamil_example_sentence",
                "word_audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_{lesson_number}_vocab_1_word.mp3",
                "example_audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_{lesson_number}_vocab_1_example.mp3"
            }}
        ],
        "conversations": [
            {{
                "title": "conversation_title",
                "exchanges": [
                    {{
                        "text": "tamil_text",
                        "speaker": "Teacher",
                        "translation": "english_translation"
                    }},
                    {{
                        "text": "tamil_response",
                        "speaker": "Student", 
                        "translation": "english_translation"
                    }}
                ]
            }}
        ],
        "grammar_points": [
            {{
                "rule": "grammar_rule_name",
                "explanation": "clear_explanation_in_english",
                "examples": ["tamil_example_1", "tamil_example_2"],
                "tips": "helpful_learning_tip"
            }}
        ],
        "exercises": [
            {{
                "type": "multiple_choice",
                "question": "tamil_question",
                "options": ["option1", "option2", "option3", "option4"],
                "correct_answer": "correct_option"
            }}
        ]
    }}
    
    Focus on:
    - Authentic Chennai Tamil dialect
    - Practical, everyday usage for A1 beginners
    - Progressive difficulty within A1 level
    - Cultural context and relevance
    - Clear, simple grammar explanations
    
    Make sure to generate exactly 25 vocabulary items, 15 conversation exchanges, 10 grammar points, and 5 exercises.
    """
    
    try:
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.7,
            max_tokens=4000
        )
        
        content = response.choices[0].message.content
        
        # Extract JSON from response
        if "```json" in content:
            content = content.split("```json")[1].split("```")[0]
        elif "```" in content:
            content = content.split("```")[1].split("```")[0]
        
        lesson_data = json.loads(content.strip())
        
        # Add required fields
        lesson_data['sequence_order'] = lesson_number
        lesson_data['lesson_type'] = 'vocabulary'
        
        # Update audio URLs for all vocabulary items
        for i, vocab in enumerate(lesson_data.get('vocabulary', []), 1):
            vocab['word_audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_{lesson_number}_vocab_{i}_word.mp3"
            vocab['example_audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_{lesson_number}_vocab_{i}_example.mp3"
        
        return lesson_data
        
    except Exception as e:
        print(f"❌ Failed to generate {topic}: {e}")
        return None

def main():
    """Generate all 14 new A1 lessons"""
    print("🚀 NIRA Simple Content Generator - Creating 14 New A1 Lessons")
    print("=" * 70)
    
    generated_lessons = []
    
    for i, topic in enumerate(A1_LESSON_TOPICS, start=12):
        print(f"\n📚 Generating Lesson {i}: {topic}")
        print("-" * 50)
        
        lesson_content = generate_lesson_content(topic, i)
        
        if lesson_content:
            generated_lessons.append(lesson_content)
            vocab_count = len(lesson_content.get('vocabulary', []))
            conv_count = sum(len(conv.get('exchanges', [])) for conv in lesson_content.get('conversations', []))
            grammar_count = len(lesson_content.get('grammar_points', []))
            exercise_count = len(lesson_content.get('exercises', []))
            
            print(f"✅ Generated: {vocab_count} vocab, {conv_count} conversations, {grammar_count} grammar, {exercise_count} exercises")
        else:
            print(f"❌ Failed to generate content for {topic}")
        
        # Rate limiting to avoid API limits
        time.sleep(3)
    
    # Save generated content
    output_file = 'generated_a1_lessons.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(generated_lessons, f, ensure_ascii=False, indent=2)
    
    print(f"\n🎉 Content Generation Complete!")
    print(f"📁 Saved to: {output_file}")
    print(f"📊 Generated {len(generated_lessons)} lessons")
    print(f"🎯 Total A1 lessons will be: {11 + len(generated_lessons)}")
    
    # Show summary
    total_vocab = sum(len(lesson.get('vocabulary', [])) for lesson in generated_lessons)
    total_conversations = sum(sum(len(conv.get('exchanges', [])) for conv in lesson.get('conversations', [])) for lesson in generated_lessons)
    total_grammar = sum(len(lesson.get('grammar_points', [])) for lesson in generated_lessons)
    total_exercises = sum(len(lesson.get('exercises', [])) for lesson in generated_lessons)
    
    print(f"\n📊 Content Summary:")
    print(f"   • Total Vocabulary: {total_vocab}")
    print(f"   • Total Conversations: {total_conversations}")
    print(f"   • Total Grammar Points: {total_grammar}")
    print(f"   • Total Exercises: {total_exercises}")

if __name__ == "__main__":
    main()
