#!/usr/bin/env python3
"""
Tamil Voice Sample Generator for NIRA
Generates audio samples with different ElevenLabs voices for Tamil content
"""

import os
import requests
import json
from pathlib import Path

# ElevenLabs Configuration
ELEVENLABS_API_KEY = os.getenv('ELEVENLABS_API_KEY', '***************************************************')
BASE_URL = "https://api.elevenlabs.io/v1"

# Sample Tamil text for voice testing
SAMPLE_TEXTS = [
    "வணக்கம், நான் தமிழ் கற்பிக்கும் உதவியாளர்",  # Hello, I am a Tamil teaching assistant
    "நீங்கள் எப்படி இருக்கிறீர்கள்?",  # How are you?
    "தமிழ் மொழி மிகவும் அழகான மொழி",  # Tamil is a very beautiful language
    "நன்றி, வணக்கம்"  # Thank you, goodbye
]

# Voice configurations to test - SELECTED MALE + FEMALE VOICES
VOICE_CONFIGS = [
    # SELECTED MALE VOICES (User's favorites)
    {
        "name": "<PERSON>_Clear_MALE",
        "voice_id": "VR6AewLTigWG4xSOukaG",  # <PERSON> - <PERSON>, American
        "settings": {
            "stability": 0.8,
            "similarity_boost": 0.9,
            "style": 0.1,
            "use_speaker_boost": False
        }
    },
    {
        "name": "Sam_Natural_MALE",
        "voice_id": "yoZ06aMxZJJ28mfd3POQ",  # Sam - Male, American
        "settings": {
            "stability": 0.6,
            "similarity_boost": 0.8,
            "style": 0.3,
            "use_speaker_boost": True
        }
    },
    # FEMALE VOICES FOR REVIEW
    {
        "name": "Rachel_Professional_FEMALE",
        "voice_id": "21m00Tcm4TlvDq8ikWAM",  # Rachel - Female, American
        "settings": {
            "stability": 0.7,
            "similarity_boost": 0.8,
            "style": 0.2,
            "use_speaker_boost": True
        }
    },
    {
        "name": "Domi_Warm_FEMALE",
        "voice_id": "AZnzlk1XvdvUeBnXmlld",  # Domi - Female, American
        "settings": {
            "stability": 0.5,
            "similarity_boost": 0.75,
            "style": 0.4,
            "use_speaker_boost": True
        }
    },
    {
        "name": "Bella_Clear_FEMALE",
        "voice_id": "EXAVITQu4vr4xnSDxMaL",  # Bella - Female, American
        "settings": {
            "stability": 0.8,
            "similarity_boost": 0.9,
            "style": 0.1,
            "use_speaker_boost": False
        }
    },
    {
        "name": "Elli_Expressive_FEMALE",
        "voice_id": "MF3mGyEYCl7XYWbV9V6O",  # Elli - Female, American
        "settings": {
            "stability": 0.4,
            "similarity_boost": 0.7,
            "style": 0.6,
            "use_speaker_boost": True
        }
    },
    {
        "name": "Nicole_Natural_FEMALE",
        "voice_id": "piTKgcLEGmPE4e6mEKli",  # Nicole - Female, Australian
        "settings": {
            "stability": 0.6,
            "similarity_boost": 0.8,
            "style": 0.3,
            "use_speaker_boost": True
        }
    },
    {
        "name": "Freya_Gentle_FEMALE",
        "voice_id": "jsCqWAovK2LkecY7zXl4",  # Freya - Female, American
        "settings": {
            "stability": 0.7,
            "similarity_boost": 0.85,
            "style": 0.25,
            "use_speaker_boost": True
        }
    }
]

def get_available_voices():
    """Get all available voices from ElevenLabs"""
    if ELEVENLABS_API_KEY == 'your_api_key_here':
        print("❌ Please set ELEVENLABS_API_KEY environment variable")
        return []
    
    url = f"{BASE_URL}/voices"
    headers = {"xi-api-key": ELEVENLABS_API_KEY}
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        voices = response.json()["voices"]
        
        print(f"✅ Found {len(voices)} available voices")
        
        # Filter for multilingual voices that might work with Tamil
        multilingual_voices = []
        for voice in voices:
            if any(label.get("category") == "accent" and "indian" in label.get("name", "").lower() 
                   for label in voice.get("labels", {})):
                multilingual_voices.append(voice)
            elif "multilingual" in voice.get("name", "").lower():
                multilingual_voices.append(voice)
        
        print(f"🌍 Found {len(multilingual_voices)} potentially suitable voices for Tamil")
        return multilingual_voices
        
    except Exception as e:
        print(f"❌ Error fetching voices: {e}")
        return []

def generate_voice_sample(text, voice_config, output_dir):
    """Generate a single voice sample"""
    if ELEVENLABS_API_KEY == 'your_api_key_here':
        print("❌ Please set ELEVENLABS_API_KEY environment variable")
        return False
    
    url = f"{BASE_URL}/text-to-speech/{voice_config['voice_id']}"
    
    headers = {
        "Accept": "audio/mpeg",
        "Content-Type": "application/json",
        "xi-api-key": ELEVENLABS_API_KEY
    }
    
    data = {
        "text": text,
        "model_id": "eleven_multilingual_v2",
        "voice_settings": voice_config["settings"]
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        response.raise_for_status()
        
        # Create filename
        safe_text = text[:20].replace(" ", "_").replace(",", "").replace(".", "")
        filename = f"{voice_config['name']}_{safe_text}.mp3"
        filepath = output_dir / filename
        
        # Save audio file
        with open(filepath, 'wb') as f:
            f.write(response.content)
        
        print(f"✅ Generated: {filename}")
        return True
        
    except Exception as e:
        print(f"❌ Error generating {voice_config['name']}: {e}")
        return False

def main():
    """Generate Tamil voice samples"""
    print("🎵 Tamil Voice Sample Generator for NIRA")
    print("=" * 50)
    
    # Create output directory
    output_dir = Path("tamil_voice_samples")
    output_dir.mkdir(exist_ok=True)
    
    # Check API key
    if ELEVENLABS_API_KEY == 'your_api_key_here':
        print("❌ ElevenLabs API key not configured!")
        print("Please set the ELEVENLABS_API_KEY environment variable")
        print("Example: export ELEVENLABS_API_KEY='your_actual_api_key'")
        return
    
    # Get available voices
    print("\n🔍 Checking available voices...")
    available_voices = get_available_voices()
    
    if available_voices:
        print("\n🎤 Available multilingual voices:")
        for voice in available_voices[:5]:  # Show first 5
            print(f"  • {voice['name']} (ID: {voice['voice_id']})")
    
    # Generate samples with different configurations
    print(f"\n🎵 Generating voice samples...")
    print(f"Output directory: {output_dir.absolute()}")
    
    total_samples = 0
    successful_samples = 0
    
    for text in SAMPLE_TEXTS:
        print(f"\n📝 Text: {text}")
        
        for voice_config in VOICE_CONFIGS:
            total_samples += 1
            if generate_voice_sample(text, voice_config, output_dir):
                successful_samples += 1
    
    # Generate summary
    print(f"\n📊 Generation Summary:")
    print(f"  • Total samples attempted: {total_samples}")
    print(f"  • Successful generations: {successful_samples}")
    print(f"  • Success rate: {successful_samples/total_samples*100:.1f}%")
    
    if successful_samples > 0:
        print(f"\n🎧 Listen to samples in: {output_dir.absolute()}")
        print("Choose your preferred voice configuration and update the NIRA app!")
        
        # Create a summary file
        summary_file = output_dir / "voice_configurations.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump({
                "voice_configs": VOICE_CONFIGS,
                "sample_texts": SAMPLE_TEXTS,
                "generation_summary": {
                    "total_attempted": total_samples,
                    "successful": successful_samples,
                    "success_rate": f"{successful_samples/total_samples*100:.1f}%"
                }
            }, f, indent=2, ensure_ascii=False)
        
        print(f"📄 Configuration details saved to: {summary_file}")

if __name__ == "__main__":
    main()
