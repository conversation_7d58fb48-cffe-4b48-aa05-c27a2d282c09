#!/usr/bin/env python3
"""
Create remaining A1 lessons directly in database
"""

import requests
import json

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

headers = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

# Lesson data for remaining lessons (15-25)
lessons_data = [
    {
        "title": "Shopping and Money",
        "sequence_order": 15,
        "vocabulary": [
            {"word": "பணம்", "example": "என்னிடம் பணம் இல்லை", "translation": "Money"},
            {"word": "விலை", "example": "இதன் விலை என்ன?", "translation": "Price"},
            {"word": "வாங்கு", "example": "நான் பால் வாங்குகிறேன்", "translation": "Buy"},
            {"word": "விற்கு", "example": "அவர் பழம் விற்கிறார்", "translation": "Sell"},
            {"word": "கடை", "example": "கடையில் செல்வோம்", "translation": "Shop"}
        ]
    },
    {
        "title": "Directions and Locations",
        "sequence_order": 16,
        "vocabulary": [
            {"word": "வலது", "example": "வலது பக்கம் திரும்பு", "translation": "Right"},
            {"word": "இடது", "example": "இடது பக்கம் செல்", "translation": "Left"},
            {"word": "நேராக", "example": "நேராக செல்லுங்கள்", "translation": "Straight"},
            {"word": "அருகில்", "example": "வங்கி அருகில் இருக்கிறது", "translation": "Near"},
            {"word": "தூரம்", "example": "அது தூரம் இல்லை", "translation": "Distance"}
        ]
    },
    {
        "title": "Health and Body",
        "sequence_order": 17,
        "vocabulary": [
            {"word": "உடல்", "example": "என் உடல் நலமாக இருக்கிறது", "translation": "Body"},
            {"word": "வலி", "example": "தலையில் வலி இருக்கிறது", "translation": "Pain"},
            {"word": "மருத்துவர்", "example": "மருத்துவரை பார்க்க வேண்டும்", "translation": "Doctor"},
            {"word": "மருந்து", "example": "மருந்து சாப்பிட வேண்டும்", "translation": "Medicine"},
            {"word": "நலம்", "example": "நீங்கள் நலமாக இருக்கிறீர்களா?", "translation": "Health"}
        ]
    },
    {
        "title": "Hobbies and Interests",
        "sequence_order": 18,
        "vocabulary": [
            {"word": "விளையாட்டு", "example": "நான் விளையாட்டு விளையாடுகிறேன்", "translation": "Sports"},
            {"word": "படிக்க", "example": "நான் புத்தகம் படிக்கிறேன்", "translation": "Read"},
            {"word": "பாட", "example": "அவள் பாட்டு பாடுகிறாள்", "translation": "Sing"},
            {"word": "நடனம்", "example": "நடனம் ஆட விரும்புகிறேன்", "translation": "Dance"},
            {"word": "சினிமா", "example": "சினிமா பார்க்க போகிறோம்", "translation": "Movie"}
        ]
    },
    {
        "title": "Work and Professions",
        "sequence_order": 19,
        "vocabulary": [
            {"word": "ஆசிரியர்", "example": "அவர் ஆசிரியர்", "translation": "Teacher"},
            {"word": "மருத்துவர்", "example": "என் அப்பா மருத்துவர்", "translation": "Doctor"},
            {"word": "பொறியாளர்", "example": "அவன் பொறியாளர்", "translation": "Engineer"},
            {"word": "வணிகர்", "example": "அவர் வணிகர்", "translation": "Businessman"},
            {"word": "விவசாயி", "example": "என் தாத்தா விவசாயி", "translation": "Farmer"}
        ]
    },
    {
        "title": "Education and School",
        "sequence_order": 20,
        "vocabulary": [
            {"word": "பள்ளி", "example": "நான் பள்ளிக்கு செல்கிறேன்", "translation": "School"},
            {"word": "புத்தகம்", "example": "புத்தகம் படிக்கிறேன்", "translation": "Book"},
            {"word": "எழுது", "example": "நான் கடிதம் எழுதுகிறேன்", "translation": "Write"},
            {"word": "கற்று", "example": "தமிழ் கற்றுக்கொள்கிறேன்", "translation": "Learn"},
            {"word": "தேர்வு", "example": "நாளை தேர்வு இருக்கிறது", "translation": "Exam"}
        ]
    },
    {
        "title": "Technology and Communication",
        "sequence_order": 21,
        "vocabulary": [
            {"word": "தொலைபேசி", "example": "என் தொலைபேசி எங்கே?", "translation": "Phone"},
            {"word": "கணினி", "example": "கணினியில் வேலை செய்கிறேன்", "translation": "Computer"},
            {"word": "இணையம்", "example": "இணையம் இணைக்கப்பட்டுள்ளது", "translation": "Internet"},
            {"word": "செய்தி", "example": "செய்தி அனுப்புகிறேன்", "translation": "Message"},
            {"word": "மின்னஞ்சல்", "example": "மின்னஞ்சல் பார்க்கிறேன்", "translation": "Email"}
        ]
    },
    {
        "title": "Emotions and Feelings",
        "sequence_order": 22,
        "vocabulary": [
            {"word": "மகிழ்ச்சி", "example": "நான் மகிழ்ச்சியாக இருக்கிறேன்", "translation": "Happy"},
            {"word": "சோகம்", "example": "அவன் சோகமாக இருக்கிறான்", "translation": "Sad"},
            {"word": "கோபம்", "example": "அவள் கோபமாக இருக்கிறாள்", "translation": "Angry"},
            {"word": "பயம்", "example": "எனக்கு பயமாக இருக்கிறது", "translation": "Fear"},
            {"word": "அன்பு", "example": "அம்மா மீது அன்பு", "translation": "Love"}
        ]
    },
    {
        "title": "Festivals and Celebrations",
        "sequence_order": 23,
        "vocabulary": [
            {"word": "திருவிழா", "example": "இன்று திருவிழா", "translation": "Festival"},
            {"word": "பொங்கல்", "example": "பொங்கல் கொண்டாடுகிறோம்", "translation": "Pongal"},
            {"word": "தீபாவளி", "example": "தீபாவளி வாழ்த்துக்கள்", "translation": "Diwali"},
            {"word": "கொண்டாட", "example": "பிறந்தநாள் கொண்டாடுகிறோம்", "translation": "Celebrate"},
            {"word": "பரிசு", "example": "பரிசு கொடுக்கிறேன்", "translation": "Gift"}
        ]
    },
    {
        "title": "Animals and Nature",
        "sequence_order": 24,
        "vocabulary": [
            {"word": "நாய்", "example": "என் நாய் அழகானது", "translation": "Dog"},
            {"word": "பூனை", "example": "பூனை பால் குடிக்கிறது", "translation": "Cat"},
            {"word": "மரம்", "example": "பெரிய மரம் இருக்கிறது", "translation": "Tree"},
            {"word": "பூ", "example": "அழகான பூ பூத்துள்ளது", "translation": "Flower"},
            {"word": "பறவை", "example": "பறவை பறக்கிறது", "translation": "Bird"}
        ]
    },
    {
        "title": "Travel and Transportation Advanced",
        "sequence_order": 25,
        "vocabulary": [
            {"word": "பயணம்", "example": "நாங்கள் பயணம் செல்கிறோம்", "translation": "Travel"},
            {"word": "ரயில் நிலையம்", "example": "ரயில் நிலையம் அருகில் உள்ளது", "translation": "Railway Station"},
            {"word": "விமான நிலையம்", "example": "விமான நிலையம் தூரம்", "translation": "Airport"},
            {"word": "டிக்கெட்", "example": "டிக்கெட் வாங்க வேண்டும்", "translation": "Ticket"},
            {"word": "சாமான்", "example": "சாமான் தயார் செய்கிறேன்", "translation": "Luggage"}
        ]
    }
]

def create_lesson(lesson_data):
    """Create a single lesson in the database"""
    
    # Build vocabulary with audio URLs
    vocabulary = []
    for i, vocab in enumerate(lesson_data["vocabulary"], 1):
        vocab_item = {
            "word": vocab["word"],
            "example": vocab["example"],
            "translation": vocab["translation"],
            "word_audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_{lesson_data['sequence_order']}_vocab_{i}_word.mp3",
            "example_audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_{lesson_data['sequence_order']}_vocab_{i}_example.mp3"
        }
        vocabulary.append(vocab_item)
    
    # Create basic conversation
    conversations = [{
        "title": f"{lesson_data['title']} Practice",
        "exchanges": [
            {
                "text": f"{vocabulary[0]['word']} என்பதற்கு ஆங்கிலத்தில் என்ன?",
                "speaker": "Teacher",
                "translation": f"What is {vocabulary[0]['word']} in English?"
            },
            {
                "text": f"{vocabulary[0]['word']} என்பதற்கு {vocabulary[0]['translation']}",
                "speaker": "Student", 
                "translation": f"{vocabulary[0]['word']} means {vocabulary[0]['translation']}"
            }
        ]
    }]
    
    # Create basic grammar point
    grammar_points = [{
        "rule": f"{lesson_data['title']} Vocabulary",
        "explanation": f"Key vocabulary words related to {lesson_data['title']}",
        "examples": [vocabulary[0]['word'], vocabulary[1]['word']],
        "tips": "Practice using these words in daily conversation"
    }]
    
    # Create basic exercise
    exercises = [{
        "type": "multiple_choice",
        "question": f"{vocabulary[0]['word']} என்பதற்கு ஆங்கிலத்தில் என்ன?",
        "options": [vocabulary[0]['translation'], vocabulary[1]['translation'], "Other1", "Other2"],
        "correct_answer": vocabulary[0]['translation']
    }]
    
    content_metadata = {
        "vocabulary": vocabulary,
        "conversations": conversations,
        "grammar_points": grammar_points,
        "exercises": exercises
    }
    
    # Get Tamil path ID
    path_query = """
    SELECT lp.id FROM learning_paths lp 
    JOIN languages lang ON lp.language_id = lang.id 
    WHERE lang.name = 'Tamil' AND lp.name = 'Tamil A1 Course'
    """
    
    path_response = requests.post(
        f"{SUPABASE_URL}/rest/v1/rpc/execute_sql",
        headers=headers,
        json={"query": path_query}
    )
    
    if path_response.status_code != 200:
        print(f"❌ Could not get path ID: {path_response.text}")
        return False
    
    path_result = path_response.json()
    if not path_result:
        print("❌ Tamil A1 Course not found")
        return False
    
    path_id = path_result[0]['id']
    
    # Insert lesson
    lesson_payload = {
        'title': lesson_data['title'],
        'sequence_order': lesson_data['sequence_order'],
        'lesson_type': 'vocabulary',
        'path_id': path_id,
        'content_metadata': content_metadata,
        'is_active': True
    }
    
    response = requests.post(
        f"{SUPABASE_URL}/rest/v1/lessons",
        headers=headers,
        json=lesson_payload
    )
    
    if response.status_code in [200, 201]:
        print(f"✅ Created: {lesson_data['title']} (Lesson {lesson_data['sequence_order']})")
        return True
    else:
        print(f"❌ Failed to create {lesson_data['title']}: {response.text}")
        return False

def main():
    """Create all remaining lessons"""
    print("🚀 Creating Remaining A1 Lessons (15-25)")
    print("=" * 50)
    
    success_count = 0
    for lesson_data in lessons_data:
        if create_lesson(lesson_data):
            success_count += 1
    
    print(f"\n🎉 Lesson Creation Complete!")
    print(f"✅ Successfully created: {success_count}/{len(lessons_data)} lessons")
    print(f"🎯 Total A1 lessons now: {11 + success_count}")

if __name__ == "__main__":
    main()
