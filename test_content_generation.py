#!/usr/bin/env python3
"""
Test Content Generation - Create one sample lesson to verify the system works
"""

import json
import os
import requests

# Test with a simple lesson generation
def test_openai_generation():
    """Test OpenAI content generation"""
    
    # You'll need to set this - check if you have OpenAI API key
    api_key = "your_openai_key_here"  # Replace with actual key
    
    if api_key == "your_openai_key_here":
        print("❌ Please set your OpenAI API key in the script")
        return None
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    prompt = """
    Create a Tamil A1 beginner lesson for "Personal Information and Identity".
    
    Generate exactly:
    - 25 vocabulary items (Tamil word, English translation, Tamil example)
    - 15 conversation exchanges (realistic dialogues)
    - 10 grammar points (rules, explanations, examples)
    - 5 practice exercises (varied types)
    
    Return as valid JSON with this structure:
    {
        "title": "Personal Information and Identity",
        "vocabulary": [
            {
                "word": "tamil_word",
                "translation": "english_translation",
                "example": "tamil_example_sentence"
            }
        ],
        "conversations": [
            {
                "title": "conversation_title",
                "exchanges": [
                    {
                        "text": "tamil_text",
                        "speaker": "Teacher/Student",
                        "translation": "english_translation"
                    }
                ]
            }
        ],
        "grammar_points": [
            {
                "rule": "grammar_rule_name",
                "explanation": "clear_explanation",
                "examples": ["tamil_example_1", "tamil_example_2"],
                "tips": "helpful_learning_tip"
            }
        ],
        "exercises": [
            {
                "type": "multiple_choice",
                "question": "tamil_question",
                "options": ["option1", "option2", "option3", "option4"],
                "correct_answer": "correct_option"
            }
        ]
    }
    
    Focus on authentic Chennai Tamil for A1 beginners.
    """
    
    data = {
        "model": "gpt-4o-mini",
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.7,
        "max_tokens": 4000
    }
    
    try:
        response = requests.post(
            "https://api.openai.com/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            
            # Extract JSON from response
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0]
            elif "```" in content:
                content = content.split("```")[1].split("```")[0]
            
            lesson_data = json.loads(content.strip())
            
            # Add required fields
            lesson_data['sequence_order'] = 12
            lesson_data['lesson_type'] = 'vocabulary'
            
            # Add audio URLs to vocabulary
            for i, vocab in enumerate(lesson_data.get('vocabulary', []), 1):
                vocab['word_audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_12_vocab_{i}_word.mp3"
                vocab['example_audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_tamil_a1_lesson_12_vocab_{i}_example.mp3"
            
            print("✅ Test lesson generated successfully!")
            print(f"📊 Vocabulary: {len(lesson_data.get('vocabulary', []))}")
            print(f"📊 Conversations: {len(lesson_data.get('conversations', []))}")
            print(f"📊 Grammar: {len(lesson_data.get('grammar_points', []))}")
            print(f"📊 Exercises: {len(lesson_data.get('exercises', []))}")
            
            # Save test lesson
            with open('test_lesson.json', 'w', encoding='utf-8') as f:
                json.dump(lesson_data, f, ensure_ascii=False, indent=2)
            
            print("💾 Saved to: test_lesson.json")
            return lesson_data
            
        else:
            print(f"❌ OpenAI API error: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_supabase_insertion():
    """Test inserting the lesson into Supabase"""
    
    # Load test lesson
    try:
        with open('test_lesson.json', 'r', encoding='utf-8') as f:
            lesson_data = json.load(f)
    except FileNotFoundError:
        print("❌ No test lesson found. Run content generation first.")
        return False
    
    # You'll need to set your Supabase key
    supabase_key = "your_supabase_key_here"  # Replace with actual key
    
    if supabase_key == "your_supabase_key_here":
        print("❌ Please set your Supabase key in the script")
        return False
    
    # Get Tamil path ID first
    headers = {
        'apikey': supabase_key,
        'Authorization': f'Bearer {supabase_key}',
        'Content-Type': 'application/json'
    }
    
    # Get path ID
    path_response = requests.post(
        "https://lyaojebttnqilmdosmjk.supabase.co/rest/v1/rpc/execute_sql",
        headers=headers,
        json={
            "query": "SELECT lp.id FROM learning_paths lp JOIN languages lang ON lp.language_id = lang.id WHERE lang.name = 'Tamil' AND lp.name = 'Tamil A1 Course'"
        }
    )
    
    if path_response.status_code != 200:
        print(f"❌ Could not get path ID: {path_response.text}")
        return False
    
    path_result = path_response.json()
    if not path_result:
        print("❌ Tamil A1 Course not found")
        return False
    
    path_id = path_result[0]['id']
    print(f"📍 Tamil A1 Course Path ID: {path_id}")
    
    # Insert lesson
    lesson_payload = {
        'title': lesson_data['title'],
        'sequence_order': lesson_data['sequence_order'],
        'lesson_type': lesson_data['lesson_type'],
        'path_id': path_id,
        'content_metadata': {
            'vocabulary': lesson_data.get('vocabulary', []),
            'conversations': lesson_data.get('conversations', []),
            'grammar_points': lesson_data.get('grammar_points', []),
            'exercises': lesson_data.get('exercises', [])
        },
        'is_active': True
    }
    
    response = requests.post(
        "https://lyaojebttnqilmdosmjk.supabase.co/rest/v1/lessons",
        headers=headers,
        json=lesson_payload
    )
    
    if response.status_code in [200, 201]:
        print("✅ Test lesson inserted into database!")
        return True
    else:
        print(f"❌ Database insertion failed: {response.text}")
        return False

def main():
    """Run the test"""
    print("🧪 NIRA Content Generation Test")
    print("=" * 40)
    
    print("\n📚 Step 1: Testing Content Generation")
    lesson = test_openai_generation()
    
    if lesson:
        print("\n💾 Step 2: Testing Database Insertion")
        success = test_supabase_insertion()
        
        if success:
            print("\n🎉 Test Complete - System Ready!")
            print("✅ Content generation works")
            print("✅ Database insertion works")
            print("🚀 Ready to create all 14 lessons")
        else:
            print("\n⚠️  Content generation works, but database insertion failed")
    else:
        print("\n❌ Content generation failed")

if __name__ == "__main__":
    main()
